# Emm_V5_Vel_Control 步进电机移植指南

## 1. 概述

本文档详细介绍如何单独移植使用 `Emm_V5_Vel_Control` 函数控制的步进电机系统。该系统基于 Emm V5.0 智能步进电机，通过 UART 串口通信实现速度控制。

## 2. 硬件要求

### 2.1 主控芯片
- STM32F4xx 系列微控制器（推荐 STM32F407VET6）
- 支持 UART 通信功能

### 2.2 步进电机
- Emm V5.0 智能步进电机
- 支持串口通信协议
- 工作电压：12V-24V
- 最大转速：5000 RPM

### 2.3 连接方式
```
STM32 UART TX  -----> Emm V5 RX
STM32 UART RX  -----> Emm V5 TX  
STM32 GND      -----> Emm V5 GND
```

## 3. 软件架构

### 3.1 文件结构
```
project/
├── app/
│   ├── Emm_V5.c          # Emm V5 驱动实现
│   └── Emm_V5.h          # Emm V5 驱动头文件
├── bsp/
│   ├── step_motor_bsp.c  # 步进电机 BSP 层
│   ├── step_motor_bsp.h  # 步进电机 BSP 头文件
│   ├── uart_bsp.c        # UART BSP 层
│   └── uart_bsp.h        # UART BSP 头文件
└── Core/
    ├── Inc/
    │   └── usart.h       # STM32 UART 配置
    └── Src/
        └── usart.c       # STM32 UART 实现
```

### 3.2 依赖关系
```mermaid
graph TD
    A[应用层] --> B[step_motor_bsp.c]
    B --> C[Emm_V5.c]
    C --> D[uart_bsp.c]
    D --> E[STM32 HAL UART]
    
    B --> F[step_motor_bsp.h]
    C --> G[Emm_V5.h]
    D --> H[uart_bsp.h]
```

## 4. 核心函数详解

### 4.1 Emm_V5_Vel_Control 函数

```c
/**
 * @brief    速度模式控制
 * @param    huart 串口句柄
 * @param    addr  电机地址
 * @param    dir   方向控制 (0为CW，非零值为CCW)
 * @param    vel   目标速度 (范围0-5000 RPM，单位0.1RPM)
 * @param    acc   加速度值 (范围0-255，注意：0表示直接启动)
 * @param    snF   多轴同步运动标志 (false为不启用，true为启用)
 * @retval   无
 */
void Emm_V5_Vel_Control(UART_HandleTypeDef* huart, uint8_t addr, 
                        uint8_t dir, uint16_t vel, uint8_t acc, bool snF);
```

### 4.2 通信协议格式

| 字节位置 | 内容 | 说明 |
|---------|------|------|
| 0 | addr | 电机地址 (0x01-0xFF) |
| 1 | 0xF6 | 速度控制命令字 |
| 2 | dir | 方向 (0=CW正转, 1=CCW反转) |
| 3 | vel_high | 速度高8位 |
| 4 | vel_low | 速度低8位 |
| 5 | acc | 加速度 (0-255) |
| 6 | snF | 同步标志 (0/1) |
| 7 | 0x6B | 校验字节 |

### 4.3 速度计算

```c
// 速度单位转换：RPM -> 0.1RPM
uint16_t speed_scaled = (uint16_t)(rpm_value * 10 + 0.5f);

// 示例：设置 100.5 RPM
// speed_scaled = (uint16_t)(100.5 * 10 + 0.5) = 1005
```

## 5. 移植步骤

### 5.1 第一步：复制核心文件

将以下文件复制到您的项目中：

1. **app/Emm_V5.c** - 核心驱动实现
2. **app/Emm_V5.h** - 驱动头文件
3. **bsp/uart_bsp.c** - UART BSP层（可选，用于调试）
4. **bsp/uart_bsp.h** - UART BSP头文件

### 5.2 第二步：配置 UART

在 STM32CubeMX 中配置 UART：

```c
// usart.h 中的配置示例
extern UART_HandleTypeDef huart2;  // 用于电机通信
extern UART_HandleTypeDef huart1;  // 用于调试输出（可选）

// UART 参数配置
// 波特率：9600
// 数据位：8
// 停止位：1
// 校验位：无
// 流控制：无
```

### 5.3 第三步：创建电机控制接口

创建简化的电机控制文件：

```c
// simple_motor.h
#ifndef __SIMPLE_MOTOR_H__
#define __SIMPLE_MOTOR_H__

#include "Emm_V5.h"
#include "usart.h"

// 电机配置参数
#define MOTOR_ADDR          0x01        // 电机地址
#define MOTOR_UART          huart2      // 使用的串口
#define MOTOR_MAX_SPEED     3000        // 最大转速(RPM)
#define MOTOR_ACCEL         50          // 加速度值
#define MOTOR_SYNC_FLAG     false       // 同步标志

// 函数声明
void Motor_Init(void);
void Motor_Set_Speed(float rpm);
void Motor_Stop(void);

#endif
```

```c
// simple_motor.c
#include "simple_motor.h"

/**
 * @brief 电机初始化
 */
void Motor_Init(void)
{
    // 使能电机
    Emm_V5_En_Control(&MOTOR_UART, MOTOR_ADDR, true, MOTOR_SYNC_FLAG);
    
    // 初始停止
    Motor_Stop();
}

/**
 * @brief 设置电机速度
 * @param rpm 目标转速，支持负值表示反向
 */
void Motor_Set_Speed(float rpm)
{
    uint8_t dir;
    uint16_t speed_scaled;
    float abs_rpm;
    
    // 限制速度范围
    if (rpm > MOTOR_MAX_SPEED) rpm = MOTOR_MAX_SPEED;
    if (rpm < -MOTOR_MAX_SPEED) rpm = -MOTOR_MAX_SPEED;
    
    // 确定方向
    if (rpm >= 0.0f) {
        dir = 0;  // CW方向
        abs_rpm = rpm;
    } else {
        dir = 1;  // CCW方向
        abs_rpm = -rpm;
    }
    
    // 转换为0.1RPM单位
    speed_scaled = (uint16_t)(abs_rpm * 10 + 0.5f);
    
    // 发送控制命令
    Emm_V5_Vel_Control(&MOTOR_UART, MOTOR_ADDR, dir, speed_scaled, 
                       MOTOR_ACCEL, MOTOR_SYNC_FLAG);
}

/**
 * @brief 停止电机
 */
void Motor_Stop(void)
{
    Emm_V5_Stop_Now(&MOTOR_UART, MOTOR_ADDR, MOTOR_SYNC_FLAG);
}
```

## 6. 使用示例

### 6.1 基本使用

```c
#include "simple_motor.h"

int main(void)
{
    // 系统初始化
    HAL_Init();
    SystemClock_Config();
    MX_USART2_UART_Init();  // 初始化电机通信串口
    
    // 电机初始化
    Motor_Init();
    
    while(1)
    {
        // 正转 100 RPM
        Motor_Set_Speed(100.0f);
        HAL_Delay(3000);
        
        // 停止
        Motor_Stop();
        HAL_Delay(1000);
        
        // 反转 50 RPM
        Motor_Set_Speed(-50.0f);
        HAL_Delay(3000);
        
        // 停止
        Motor_Stop();
        HAL_Delay(1000);
    }
}
```

### 6.2 多电机控制

```c
// 定义多个电机
#define MOTOR1_ADDR    0x01
#define MOTOR2_ADDR    0x02

void Multi_Motor_Control(void)
{
    // 同时控制两个电机
    Emm_V5_Vel_Control(&huart2, MOTOR1_ADDR, 0, 1000, 50, false);  // 电机1正转100RPM
    Emm_V5_Vel_Control(&huart2, MOTOR2_ADDR, 1, 500, 50, false);   // 电机2反转50RPM
}
```

## 7. 调试与故障排除

### 7.1 常见问题

1. **电机不响应**
   - 检查串口连接和配置
   - 确认电机地址设置正确
   - 检查电机供电

2. **速度不准确**
   - 确认速度单位转换正确（0.1RPM）
   - 检查加速度参数设置

3. **通信错误**
   - 检查波特率设置（默认9600）
   - 确认数据格式（8N1）

### 7.2 调试工具

```c
// 添加调试输出（需要额外的调试串口）
void Debug_Print_Command(uint8_t addr, uint8_t dir, uint16_t vel)
{
    printf("Motor Addr: %d, Dir: %d, Speed: %d (0.1RPM)\r\n", 
           addr, dir, vel);
}
```

## 8. 性能优化

### 8.1 通信优化
- 使用 DMA 传输减少 CPU 占用
- 实现命令队列避免频繁发送

### 8.2 控制优化
- 实现平滑加减速算法
- 添加位置反馈控制

## 9. 扩展功能

### 9.1 位置控制
可以扩展使用 `Emm_V5_Pos_Control` 实现精确位置控制。

### 9.2 状态监控
使用 `Emm_V5_Read_Sys_Params` 读取电机状态信息。

## 10. 总结

本移植指南提供了完整的 Emm_V5_Vel_Control 电机控制系统移植方案。通过遵循本指南，您可以快速在自己的项目中实现步进电机的速度控制功能。

关键要点：
- 正确配置 UART 通信参数
- 理解速度单位转换（0.1RPM）
- 合理设置加速度参数
- 实现适当的错误处理机制
