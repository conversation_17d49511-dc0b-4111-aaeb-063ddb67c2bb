# STM32电控工程教学文档 - 系统架构设计

**文档版本**: v1.0  
**创建日期**: 2025-01-26  
**负责人**: Bob (架构师)  
**版权归属**: 米醋电子工作室  

---

## 1. 架构概述

### 1.1 设计目标
本架构设计旨在为STM32电控工程教学文档提供清晰、系统化的技术架构，确保学习者能够：
- 理解项目的整体架构设计思想
- 掌握分层架构的实现原理
- 学会模块化编程的最佳实践
- 理解数据流向和变量传递机制

### 1.2 架构原则
- **分层解耦**: 采用分层架构，各层职责明确，降低耦合度
- **模块化设计**: 功能模块独立，便于维护和扩展
- **接口标准化**: 统一的接口设计，提高代码复用性
- **数据驱动**: 清晰的数据流向，便于理解和调试

---

## 2. 系统分层架构

### 2.1 四层架构模型

```
┌─────────────────────────────────────────────────────────────┐
│                    应用层 (Application Layer)                │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │ PID控制算法 │ │ 电机驱动逻辑│ │ 传感器处理  │ │ 通信协议│ │
│  │ mypid.c     │ │motor_driver │ │hwt101_driver│ │Emm_V5.c │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
└─────────────────────────────────────────────────────────────┘
                              ↓ 调用接口
┌─────────────────────────────────────────────────────────────┐
│                  BSP层 (Board Support Package)              │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │ 任务调度    │ │ 外设驱动    │ │ 硬件抽象    │ │ 中间件  │ │
│  │ schedule.c  │ │ *_bsp.c     │ │ 接口封装    │ │ringbuffer│ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
└─────────────────────────────────────────────────────────────┘
                              ↓ 调用HAL函数
┌─────────────────────────────────────────────────────────────┐
│                   HAL层 (Hardware Abstraction Layer)        │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │ STM32 HAL库 │ │ CMSIS接口   │ │ 中断处理    │ │ 系统配置│ │
│  │ stm32f4xx_* │ │ 标准接口    │ │ stm32f4xx_it│ │ main.c  │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
└─────────────────────────────────────────────────────────────┘
                              ↓ 直接操作寄存器
┌─────────────────────────────────────────────────────────────┐
│                     硬件层 (Hardware Layer)                 │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │ STM32F407   │ │ 传感器模块  │ │ 电机驱动    │ │ 通信接口│ │
│  │ 微控制器    │ │ HWT101/编码器│ │ TB6612/步进 │ │ UART/I2C│ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 2.2 各层职责详述

#### 2.2.1 应用层 (Application Layer)
**职责**: 实现具体的业务逻辑和算法
**核心模块**:
- **PID控制算法** (`mypid.c/h`): 实现PID控制器的核心算法
- **电机驱动逻辑** (`motor_driver.c/h`): 电机控制的高层逻辑
- **传感器处理** (`hwt101_driver.c/h`, `encoder_drv.c/h`): 传感器数据处理算法
- **通信协议** (`Emm_V5.c/h`, `hardware_iic.c/h`): 通信协议实现

**设计特点**:
- 与硬件无关，专注于算法实现
- 通过标准接口调用BSP层服务
- 易于测试和移植

#### 2.2.2 BSP层 (Board Support Package)
**职责**: 提供硬件抽象和中间件服务
**核心模块**:
- **任务调度** (`schedule.c/h`): 简单的任务调度器
- **外设驱动** (`*_bsp.c/h`): 各种外设的驱动封装
- **硬件抽象**: 统一的硬件接口
- **中间件** (`ringbuffer.c/h`, `oled.c/h`): 通用功能组件

**设计特点**:
- 封装硬件细节，提供统一接口
- 可配置和可扩展
- 便于硬件平台移植

#### 2.2.3 HAL层 (Hardware Abstraction Layer)
**职责**: 提供标准化的硬件访问接口
**核心组件**:
- **STM32 HAL库**: ST官方提供的硬件抽象库
- **CMSIS接口**: ARM标准的微控制器软件接口
- **中断处理** (`stm32f4xx_it.c`): 统一的中断服务程序
- **系统配置** (`main.c`): 系统初始化和配置

#### 2.2.4 硬件层 (Hardware Layer)
**职责**: 物理硬件和外设
**主要组件**:
- **STM32F407微控制器**: 核心处理器
- **传感器模块**: HWT101姿态传感器、编码器等
- **执行器**: TB6612电机驱动、步进电机等
- **通信接口**: UART、I2C等通信外设

---

## 3. 模块依赖关系

### 3.1 依赖关系图

```
main.c (系统入口)
├── HAL_Init() ──────────────────┐
├── SystemClock_Config() ────────┤
├── MX_GPIO_Init() ──────────────┤ HAL层初始化
├── MX_DMA_Init() ───────────────┤
├── MX_I2C1_Init() ──────────────┤
├── MX_TIM1_Init() ──────────────┤
└── MX_USART1_UART_Init() ───────┘
    │
    ├── schedule_init() ─────────┐
    ├── OLED_Init() ─────────────┤
    ├── PID_INIT() ──────────────┤ BSP层初始化
    ├── rt_ringbuffer_init() ────┤
    └── Step_Motor_Init() ───────┘
        │
        └── while(1) ────────────┐ 主循环
            ├── OLED_ShowStr()   │
            └── schedule_run()   │ (注释掉)
```

### 3.2 模块间接口设计

#### 3.2.1 应用层接口
```c
// PID控制器接口
typedef struct {
    float kp, ki, kd;           // PID参数
    float target, current;      // 目标值和当前值
    float error, last_error;    // 误差值
    float integral, derivative; // 积分和微分项
    float output;              // 输出值
} PID_TypeDef;

// 核心接口函数
void PID_INIT(void);                    // PID初始化
float pid_calc(PID_TypeDef* pid);       // PID计算
void pid_reset(PID_TypeDef* pid);       // PID复位
```

#### 3.2.2 BSP层接口
```c
// 任务调度器接口
typedef struct {
    void (*task_func)(void);    // 任务函数指针
    uint32_t period;           // 执行周期
    uint32_t last_run;         // 上次执行时间
} Task_TypeDef;

// 核心接口函数
void schedule_init(void);              // 调度器初始化
void schedule_run(void);               // 运行调度器
void schedule_add_task(Task_TypeDef* task); // 添加任务
```

#### 3.2.3 环形缓冲区接口
```c
// 环形缓冲区结构
typedef struct {
    uint8_t* buffer;           // 缓冲区指针
    uint32_t size;             // 缓冲区大小
    uint32_t head;             // 头指针
    uint32_t tail;             // 尾指针
} RingBuffer_TypeDef;

// 核心接口函数
void rt_ringbuffer_init(RingBuffer_TypeDef* rb, uint8_t* buffer, uint32_t size);
uint32_t rt_ringbuffer_put(RingBuffer_TypeDef* rb, const uint8_t* data, uint32_t length);
uint32_t rt_ringbuffer_get(RingBuffer_TypeDef* rb, uint8_t* data, uint32_t length);
```

---

## 4. 数据流向分析

### 4.1 传感器数据流

```
传感器硬件 → 中断/DMA → HAL层接收 → BSP层处理 → 应用层算法 → 控制输出

具体流程:
HWT101传感器 → UART中断 → HAL_UART_RxCpltCallback() → 
hwt101_bsp_update() → hwt101_data_process() → PID控制算法 → 
电机控制输出
```

### 4.2 控制指令流

```
用户输入/算法输出 → 应用层处理 → BSP层接口 → HAL层函数 → 硬件寄存器

具体流程:
PID输出 → motor_set_speed() → motor_bsp_set_pwm() → 
HAL_TIM_PWM_Start() → TIMx->CCRx寄存器 → PWM输出
```

### 4.3 通信数据流

```
外部设备 → 串口接收 → DMA/中断 → 环形缓冲区 → 数据解析 → 业务处理

具体流程:
树莓派指令 → UART接收 → HAL_UART_RxCpltCallback() → 
rt_ringbuffer_put() → 数据解析 → 执行相应动作
```

---

## 5. 关键算法架构

### 5.1 PID控制算法架构

```c
// PID算法的核心实现架构
float pid_calc(PID_TypeDef* pid) {
    // 1. 计算误差
    pid->error = pid->target - pid->current;
    
    // 2. 积分项计算 (防积分饱和)
    pid->integral += pid->error;
    if (pid->integral > MAX_INTEGRAL) 
        pid->integral = MAX_INTEGRAL;
    
    // 3. 微分项计算
    pid->derivative = pid->error - pid->last_error;
    
    // 4. PID输出计算
    pid->output = pid->kp * pid->error + 
                  pid->ki * pid->integral + 
                  pid->kd * pid->derivative;
    
    // 5. 输出限幅
    pid->output = abs_limit(pid->output, MAX_OUTPUT);
    
    // 6. 更新历史值
    pid->last_error = pid->error;
    
    return pid->output;
}
```

### 5.2 任务调度算法架构

```c
// 简单的时间片轮转调度
void schedule_run(void) {
    uint32_t current_time = HAL_GetTick();
    
    for (int i = 0; i < task_count; i++) {
        Task_TypeDef* task = &task_list[i];
        
        // 检查是否到达执行时间
        if (current_time - task->last_run >= task->period) {
            // 执行任务
            task->task_func();
            // 更新执行时间
            task->last_run = current_time;
        }
    }
}
```

---

## 6. 内存架构设计

### 6.1 内存分布

```
Flash存储器 (512KB)
├── 0x08000000 - 0x08007FFF: 启动代码 (32KB)
├── 0x08008000 - 0x0807FFFF: 应用程序代码 (480KB)
└── 保留区域

SRAM存储器 (192KB)
├── 0x20000000 - 0x20002FFF: 全局变量和静态变量 (12KB)
├── 0x20003000 - 0x2002FFFF: 堆空间 (180KB)
└── 栈空间 (向下增长)
```

### 6.2 关键数据结构

```c
// 全局PID控制器实例
PID_TypeDef motor_pid;
PID_TypeDef angle_pid;

// 传感器数据结构
typedef struct {
    float accel_x, accel_y, accel_z;    // 加速度
    float gyro_x, gyro_y, gyro_z;       // 角速度
    float angle_x, angle_y, angle_z;    // 角度
} HWT101_Data_TypeDef;

// 电机控制数据结构
typedef struct {
    int16_t speed;                      // 目标速度
    int16_t current_speed;              // 当前速度
    uint8_t direction;                  // 方向
    uint8_t enable;                     // 使能状态
} Motor_Control_TypeDef;
```

---

## 7. 错误处理架构

### 7.1 错误处理策略

```c
// 错误代码定义
typedef enum {
    ERROR_NONE = 0,
    ERROR_INIT_FAILED,
    ERROR_SENSOR_TIMEOUT,
    ERROR_MOTOR_FAULT,
    ERROR_COMMUNICATION_LOST,
    ERROR_PARAMETER_INVALID
} Error_Code_TypeDef;

// 错误处理函数
void Error_Handler(Error_Code_TypeDef error_code) {
    switch(error_code) {
        case ERROR_SENSOR_TIMEOUT:
            // 传感器超时处理
            sensor_reset();
            break;
        case ERROR_MOTOR_FAULT:
            // 电机故障处理
            motor_emergency_stop();
            break;
        default:
            // 系统复位
            NVIC_SystemReset();
            break;
    }
}
```

### 7.2 异常监控机制

- **看门狗监控**: 防止程序死循环
- **堆栈溢出检测**: 监控栈使用情况
- **通信超时检测**: 监控外设通信状态
- **参数范围检查**: 验证输入参数合法性

---

## 8. 性能优化架构

### 8.1 计算优化

```c
// 使用查表法优化三角函数计算
static const float sin_table[360] = { /* 预计算的正弦值 */ };

float fast_sin(float angle) {
    int index = (int)(angle * 180.0f / PI) % 360;
    return sin_table[index];
}

// 使用定点数优化PID计算
#define FIXED_POINT_SCALE 1000
typedef int32_t fixed_point_t;

fixed_point_t pid_calc_fixed(PID_Fixed_TypeDef* pid) {
    // 使用定点数进行PID计算，避免浮点运算
    fixed_point_t error = pid->target - pid->current;
    pid->integral += error;
    fixed_point_t derivative = error - pid->last_error;

    return (pid->kp * error + pid->ki * pid->integral + pid->kd * derivative) / FIXED_POINT_SCALE;
}
```

### 8.2 内存优化

- **数据对齐**: 确保结构体成员按4字节对齐
- **缓冲区复用**: 多个模块共享缓冲区
- **栈空间优化**: 减少局部变量使用
- **常量存储**: 将常量数据存储在Flash中

---

## 9. 可扩展性设计

### 9.1 模块化接口设计

```c
// 传感器接口抽象
typedef struct {
    void (*init)(void);
    int (*read_data)(void* data);
    void (*calibrate)(void);
    char* name;
} Sensor_Interface_TypeDef;

// 具体传感器实现
Sensor_Interface_TypeDef hwt101_sensor = {
    .init = hwt101_init,
    .read_data = hwt101_read_data,
    .calibrate = hwt101_calibrate,
    .name = "HWT101"
};

// 传感器管理器
void sensor_manager_register(Sensor_Interface_TypeDef* sensor);
void sensor_manager_read_all(void);
```

### 9.2 配置参数化

```c
// 系统配置结构
typedef struct {
    // PID参数配置
    float motor_pid_kp;
    float motor_pid_ki;
    float motor_pid_kd;

    // 传感器配置
    uint32_t sensor_update_rate;
    uint8_t sensor_filter_enable;

    // 通信配置
    uint32_t uart_baudrate;
    uint8_t communication_timeout;
} System_Config_TypeDef;

// 配置加载和保存
void config_load_from_flash(System_Config_TypeDef* config);
void config_save_to_flash(const System_Config_TypeDef* config);
```

---

## 10. 调试和测试架构

### 10.1 调试接口设计

```c
// 调试信息输出
#ifdef DEBUG_MODE
    #define DEBUG_PRINT(fmt, ...) printf("[DEBUG] " fmt "\r\n", ##__VA_ARGS__)
    #define DEBUG_ASSERT(expr) if(!(expr)) Error_Handler(ERROR_PARAMETER_INVALID)
#else
    #define DEBUG_PRINT(fmt, ...)
    #define DEBUG_ASSERT(expr)
#endif

// 性能监控
typedef struct {
    uint32_t task_start_time;
    uint32_t task_max_time;
    uint32_t task_avg_time;
    uint32_t task_count;
} Performance_Monitor_TypeDef;

void performance_monitor_start(Performance_Monitor_TypeDef* monitor);
void performance_monitor_end(Performance_Monitor_TypeDef* monitor);
```

### 10.2 单元测试框架

```c
// 简单的单元测试框架
typedef struct {
    char* test_name;
    void (*test_func)(void);
    uint8_t result;
} Unit_Test_TypeDef;

#define TEST_ASSERT(condition) \
    if (!(condition)) { \
        printf("TEST FAILED: %s at line %d\r\n", __FUNCTION__, __LINE__); \
        return; \
    }

void run_all_tests(Unit_Test_TypeDef* tests, uint32_t test_count);
```

---

## 11. 架构决策记录 (ADR)

### 11.1 ADR-001: 选择分层架构
**决策**: 采用四层分层架构设计
**理由**:
- 职责分离，便于维护
- 提高代码复用性
- 便于团队协作开发
- 易于进行单元测试

**影响**: 增加了一定的复杂度，但提高了系统的可维护性

### 11.2 ADR-002: 使用环形缓冲区
**决策**: 采用环形缓冲区处理串口数据
**理由**:
- 避免数据丢失
- 提高数据处理效率
- 支持异步数据处理
- 内存使用效率高

**影响**: 需要额外的内存空间，但提高了系统稳定性

### 11.3 ADR-003: PID算法实现方式
**决策**: 使用浮点数实现PID算法
**理由**:
- 计算精度高
- 代码易于理解
- 便于参数调节
- STM32F407支持硬件浮点运算

**影响**: 相比定点数消耗更多计算资源，但精度更高

---

## 12. 技术债务管理

### 12.1 已知技术债务

| 债务项 | 影响程度 | 解决优先级 | 预计工作量 |
|--------|----------|------------|------------|
| 缺少完整的错误处理机制 | 高 | 高 | 2天 |
| 任务调度器功能简单 | 中 | 中 | 3天 |
| 缺少配置参数持久化 | 中 | 低 | 1天 |
| 调试信息输出不完善 | 低 | 低 | 1天 |

### 12.2 重构建议

1. **引入RTOS**: 考虑使用FreeRTOS替换简单的任务调度器
2. **增强错误处理**: 实现完整的错误处理和恢复机制
3. **参数配置系统**: 实现参数的Flash存储和在线配置
4. **日志系统**: 增加完整的日志记录和分析功能

---

## 13. 总结

### 13.1 架构优势

1. **清晰的分层结构**: 四层架构职责明确，便于理解和维护
2. **模块化设计**: 各功能模块独立，便于测试和复用
3. **标准化接口**: 统一的接口设计，提高代码质量
4. **良好的扩展性**: 支持功能扩展和硬件平台移植

### 13.2 学习价值

1. **工程化思维**: 展示了真实项目的架构设计思路
2. **最佳实践**: 体现了嵌入式开发的最佳实践
3. **代码组织**: 学习如何组织复杂的嵌入式项目代码
4. **系统设计**: 理解系统级的设计考虑和权衡

### 13.3 后续改进方向

1. **性能优化**: 进一步优化算法和内存使用
2. **功能扩展**: 增加更多的传感器和执行器支持
3. **安全性**: 增强系统的安全性和可靠性
4. **用户体验**: 改善调试和配置的用户体验

---

**文档结束**

*本文档由米醋电子工作室Bob团队制作，版权所有。*
