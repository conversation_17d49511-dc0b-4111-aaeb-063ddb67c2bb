Component: ARM Compiler 5.06 update 7 (build 960) Tool: armlink [4d3601]

==============================================================================

Section Cross References

    startup_stm32f407xx.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f407xx.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f407xx.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f407xx.o(RESET) refers to startup_stm32f407xx.o(STACK) for __initial_sp
    startup_stm32f407xx.o(RESET) refers to startup_stm32f407xx.o(.text) for Reset_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.NMI_Handler) for NMI_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.SVC_Handler) for SVC_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.PendSV_Handler) for PendSV_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.DMA1_Stream2_IRQHandler) for DMA1_Stream2_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.DMA1_Stream5_IRQHandler) for DMA1_Stream5_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.USART1_IRQHandler) for USART1_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.USART2_IRQHandler) for USART2_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.USART3_IRQHandler) for USART3_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.UART4_IRQHandler) for UART4_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.DMA2_Stream1_IRQHandler) for DMA2_Stream1_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.USART6_IRQHandler) for USART6_IRQHandler
    startup_stm32f407xx.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f407xx.o(.text) refers to system_stm32f4xx.o(i.SystemInit) for SystemInit
    startup_stm32f407xx.o(.text) refers to __main.o(!!!main) for __main
    startup_stm32f407xx.o(.text) refers to startup_stm32f407xx.o(HEAP) for Heap_Mem
    startup_stm32f407xx.o(.text) refers to startup_stm32f407xx.o(STACK) for Stack_Mem
    main.o(i.SystemClock_Config) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    main.o(i.SystemClock_Config) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig) for HAL_RCC_OscConfig
    main.o(i.SystemClock_Config) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) for HAL_RCC_ClockConfig
    main.o(i.main) refers to stm32f4xx_hal.o(i.HAL_Init) for HAL_Init
    main.o(i.main) refers to main.o(i.SystemClock_Config) for SystemClock_Config
    main.o(i.main) refers to gpio.o(i.MX_GPIO_Init) for MX_GPIO_Init
    main.o(i.main) refers to dma.o(i.MX_DMA_Init) for MX_DMA_Init
    main.o(i.main) refers to i2c.o(i.MX_I2C1_Init) for MX_I2C1_Init
    main.o(i.main) refers to i2c.o(i.MX_I2C2_Init) for MX_I2C2_Init
    main.o(i.main) refers to tim.o(i.MX_TIM1_Init) for MX_TIM1_Init
    main.o(i.main) refers to tim.o(i.MX_TIM3_Init) for MX_TIM3_Init
    main.o(i.main) refers to tim.o(i.MX_TIM4_Init) for MX_TIM4_Init
    main.o(i.main) refers to usart.o(i.MX_UART5_Init) for MX_UART5_Init
    main.o(i.main) refers to usart.o(i.MX_USART1_UART_Init) for MX_USART1_UART_Init
    main.o(i.main) refers to usart.o(i.MX_USART2_UART_Init) for MX_USART2_UART_Init
    main.o(i.main) refers to usart.o(i.MX_USART3_UART_Init) for MX_USART3_UART_Init
    main.o(i.main) refers to usart.o(i.MX_USART6_UART_Init) for MX_USART6_UART_Init
    main.o(i.main) refers to usart.o(i.MX_UART4_Init) for MX_UART4_Init
    main.o(i.main) refers to schedule.o(i.schedule_init) for schedule_init
    main.o(i.main) refers to oled.o(i.OLED_Init) for OLED_Init
    main.o(i.main) refers to mypid.o(i.PID_INIT) for PID_INIT
    main.o(i.main) refers to ringbuffer.o(i.rt_ringbuffer_init) for rt_ringbuffer_init
    main.o(i.main) refers to step_motor_bsp.o(i.Step_Motor_Init) for Step_Motor_Init
    main.o(i.main) refers to emm_v5.o(i.Emm_V5_Reset_CurPos_To_Zero) for Emm_V5_Reset_CurPos_To_Zero
    main.o(i.main) refers to uart_bsp.o(i.save_initial_position) for save_initial_position
    main.o(i.main) refers to schedule.o(i.schedule_run) for schedule_run
    main.o(i.main) refers to uart_bsp.o(.bss) for ringbuffer_pool_y
    main.o(i.main) refers to uart_bsp.o(.bss) for ringbuffer_y
    main.o(i.main) refers to uart_bsp.o(.bss) for ringbuffer_pool_x
    main.o(i.main) refers to uart_bsp.o(.bss) for ringbuffer_pool_pi
    main.o(i.main) refers to usart.o(.bss) for huart4
    gpio.o(i.MX_GPIO_Init) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    gpio.o(i.MX_GPIO_Init) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    gpio.o(i.MX_GPIO_Init) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    dma.o(i.MX_DMA_Init) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    dma.o(i.MX_DMA_Init) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    i2c.o(i.HAL_I2C_MspDeInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    i2c.o(i.HAL_I2C_MspInit) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    i2c.o(i.HAL_I2C_MspInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    i2c.o(i.MX_I2C1_Init) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_Init) for HAL_I2C_Init
    i2c.o(i.MX_I2C1_Init) refers to main.o(i.Error_Handler) for Error_Handler
    i2c.o(i.MX_I2C1_Init) refers to i2c.o(.bss) for .bss
    i2c.o(i.MX_I2C2_Init) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_Init) for HAL_I2C_Init
    i2c.o(i.MX_I2C2_Init) refers to main.o(i.Error_Handler) for Error_Handler
    i2c.o(i.MX_I2C2_Init) refers to i2c.o(.bss) for .bss
    tim.o(i.HAL_TIM_Encoder_MspDeInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    tim.o(i.HAL_TIM_Encoder_MspInit) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    tim.o(i.HAL_TIM_Encoder_MspInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    tim.o(i.HAL_TIM_MspPostInit) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    tim.o(i.HAL_TIM_MspPostInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    tim.o(i.MX_TIM1_Init) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    tim.o(i.MX_TIM1_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init) for HAL_TIM_Base_Init
    tim.o(i.MX_TIM1_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM1_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) for HAL_TIM_ConfigClockSource
    tim.o(i.MX_TIM1_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init) for HAL_TIM_PWM_Init
    tim.o(i.MX_TIM1_Init) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(i.MX_TIM1_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) for HAL_TIM_PWM_ConfigChannel
    tim.o(i.MX_TIM1_Init) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigBreakDeadTime) for HAL_TIMEx_ConfigBreakDeadTime
    tim.o(i.MX_TIM1_Init) refers to tim.o(i.HAL_TIM_MspPostInit) for HAL_TIM_MspPostInit
    tim.o(i.MX_TIM1_Init) refers to tim.o(.bss) for .bss
    tim.o(i.MX_TIM3_Init) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    tim.o(i.MX_TIM3_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init) for HAL_TIM_Encoder_Init
    tim.o(i.MX_TIM3_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM3_Init) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(i.MX_TIM3_Init) refers to tim.o(.bss) for .bss
    tim.o(i.MX_TIM4_Init) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    tim.o(i.MX_TIM4_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init) for HAL_TIM_Encoder_Init
    tim.o(i.MX_TIM4_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM4_Init) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(i.MX_TIM4_Init) refers to tim.o(.bss) for .bss
    usart.o(i.HAL_UARTEx_RxEventCallback) refers to ringbuffer.o(i.rt_ringbuffer_put) for rt_ringbuffer_put
    usart.o(i.HAL_UARTEx_RxEventCallback) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    usart.o(i.HAL_UARTEx_RxEventCallback) refers to usart.o(.bss) for .bss
    usart.o(i.HAL_UARTEx_RxEventCallback) refers to uart_bsp.o(.bss) for ringbuffer_x
    usart.o(i.HAL_UART_MspDeInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    usart.o(i.HAL_UART_MspDeInit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_DeInit) for HAL_DMA_DeInit
    usart.o(i.HAL_UART_MspDeInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ) for HAL_NVIC_DisableIRQ
    usart.o(i.HAL_UART_MspInit) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    usart.o(i.HAL_UART_MspInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    usart.o(i.HAL_UART_MspInit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Init) for HAL_DMA_Init
    usart.o(i.HAL_UART_MspInit) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.HAL_UART_MspInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    usart.o(i.HAL_UART_MspInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    usart.o(i.HAL_UART_MspInit) refers to usart.o(.bss) for .bss
    usart.o(i.MX_UART4_Init) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_UART4_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_UART4_Init) refers to stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA) for HAL_UARTEx_ReceiveToIdle_DMA
    usart.o(i.MX_UART4_Init) refers to usart.o(.bss) for .bss
    usart.o(i.MX_UART5_Init) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_UART5_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_UART5_Init) refers to usart.o(.bss) for .bss
    usart.o(i.MX_USART1_UART_Init) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_USART1_UART_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_USART1_UART_Init) refers to usart.o(.bss) for .bss
    usart.o(i.MX_USART2_UART_Init) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_USART2_UART_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_USART2_UART_Init) refers to stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA) for HAL_UARTEx_ReceiveToIdle_DMA
    usart.o(i.MX_USART2_UART_Init) refers to usart.o(.bss) for .bss
    usart.o(i.MX_USART3_UART_Init) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_USART3_UART_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_USART3_UART_Init) refers to usart.o(.bss) for .bss
    usart.o(i.MX_USART6_UART_Init) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_USART6_UART_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_USART6_UART_Init) refers to stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA) for HAL_UARTEx_ReceiveToIdle_DMA
    usart.o(i.MX_USART6_UART_Init) refers to usart.o(.bss) for .bss
    stm32f4xx_it.o(i.DMA1_Stream2_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32f4xx_it.o(i.DMA1_Stream2_IRQHandler) refers to usart.o(.bss) for hdma_uart4_rx
    stm32f4xx_it.o(i.DMA1_Stream5_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32f4xx_it.o(i.DMA1_Stream5_IRQHandler) refers to usart.o(.bss) for hdma_usart2_rx
    stm32f4xx_it.o(i.DMA2_Stream1_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32f4xx_it.o(i.DMA2_Stream1_IRQHandler) refers to usart.o(.bss) for hdma_usart6_rx
    stm32f4xx_it.o(i.SysTick_Handler) refers to stm32f4xx_hal.o(i.HAL_IncTick) for HAL_IncTick
    stm32f4xx_it.o(i.UART4_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f4xx_it.o(i.UART4_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA) for HAL_UARTEx_ReceiveToIdle_DMA
    stm32f4xx_it.o(i.UART4_IRQHandler) refers to usart.o(.bss) for huart4
    stm32f4xx_it.o(i.USART1_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f4xx_it.o(i.USART1_IRQHandler) refers to usart.o(.bss) for huart1
    stm32f4xx_it.o(i.USART2_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f4xx_it.o(i.USART2_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA) for HAL_UARTEx_ReceiveToIdle_DMA
    stm32f4xx_it.o(i.USART2_IRQHandler) refers to usart.o(.bss) for huart2
    stm32f4xx_it.o(i.USART3_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f4xx_it.o(i.USART3_IRQHandler) refers to usart.o(.bss) for huart3
    stm32f4xx_it.o(i.USART6_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f4xx_it.o(i.USART6_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA) for HAL_UARTEx_ReceiveToIdle_DMA
    stm32f4xx_it.o(i.USART6_IRQHandler) refers to usart.o(.bss) for huart6
    stm32f4xx_hal_i2c.o(i.HAL_I2C_DeInit) refers to i2c.o(i.HAL_I2C_MspDeInit) for HAL_I2C_MspDeInit
    stm32f4xx_hal_i2c.o(i.HAL_I2C_ER_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_Slave_AF) for I2C_Slave_AF
    stm32f4xx_hal_i2c.o(i.HAL_I2C_ER_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_Slave_ADDR) for I2C_Slave_ADDR
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_Master_SB) for I2C_Master_SB
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_Master_ADDR) for I2C_Master_ADDR
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_TXE) for I2C_MasterTransmit_TXE
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_MemoryTransmit_TXE_BTF) for I2C_MemoryTransmit_TXE_BTF
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_BTF) for I2C_MasterTransmit_BTF
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_RXNE) for I2C_MasterReceive_RXNE
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_BTF) for I2C_MasterReceive_BTF
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF) for I2C_Slave_STOPF
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback) for HAL_I2C_SlaveRxCpltCallback
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Init) refers to i2c.o(i.HAL_I2C_MspInit) for HAL_I2C_MspInit
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Init) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32f4xx_hal_i2c.o(i.HAL_I2C_IsDeviceReady) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_IsDeviceReady) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Abort_IT) refers to stm32f4xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32f4xx_hal_i2c.o(i.I2C_MasterRequestRead) for I2C_MasterRequestRead
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal_i2c.o(i.I2C_MasterRequestWrite) for I2C_MasterRequestWrite
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout) for I2C_WaitOnBTFFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryRead) for I2C_RequestMemoryRead
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryRead) for I2C_RequestMemoryRead
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryWrite) for I2C_RequestMemoryWrite
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout) for I2C_WaitOnBTFFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryWrite) for I2C_RequestMemoryWrite
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32f4xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed) for I2C_IsAcknowledgeFailed
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.I2C_DMAAbort) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f4xx_hal_i2c.o(i.I2C_DMAAbort) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_AbortCpltCallback) for HAL_I2C_AbortCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_DMAAbort) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.I2C_DMAError) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_i2c.o(i.I2C_DMAError) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback) for HAL_I2C_SlaveRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback) for HAL_I2C_MemRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_ITError) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(i.I2C_ITError) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f4xx_hal_i2c.o(i.I2C_ITError) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_AbortCpltCallback) for HAL_I2C_AbortCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_ITError) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_ITError) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_BTF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_BTF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback) for HAL_I2C_MemRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_RXNE) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_RXNE) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_RXNE) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback) for HAL_I2C_MemRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_RXNE) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.I2C_MasterRequestRead) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_MasterRequestRead) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_MasterRequestWrite) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_MasterRequestWrite) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_BTF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MasterTxCpltCallback) for HAL_I2C_MasterTxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_BTF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback) for HAL_I2C_MemTxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_TXE) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MasterTxCpltCallback) for HAL_I2C_MasterTxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_TXE) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback) for HAL_I2C_MemTxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_TXE) refers to stm32f4xx_hal_i2c.o(i.I2C_MemoryTransmit_TXE_BTF) for I2C_MemoryTransmit_TXE_BTF
    stm32f4xx_hal_i2c.o(i.I2C_MemoryTransmit_TXE_BTF) refers to stm32f4xx_hal_i2c.o(i.I2C_Flush_DR) for I2C_Flush_DR
    stm32f4xx_hal_i2c.o(i.I2C_MemoryTransmit_TXE_BTF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback) for HAL_I2C_MemTxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryRead) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryRead) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryRead) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryWrite) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryWrite) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryWrite) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_Slave_ADDR) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_AddrCallback) for HAL_I2C_AddrCallback
    stm32f4xx_hal_i2c.o(i.I2C_Slave_AF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_Slave_AF) refers to stm32f4xx_hal_i2c.o(i.I2C_Flush_DR) for I2C_Flush_DR
    stm32f4xx_hal_i2c.o(i.I2C_Slave_AF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetState) for HAL_DMA_GetState
    stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f4xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback) for HAL_I2C_SlaveRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout) refers to stm32f4xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed) for I2C_IsAcknowledgeFailed
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) refers to stm32f4xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed) for I2C_IsAcknowledgeFailed
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32f4xx.o(.constdata) for AHBPrescTable
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(.data) for uwTickPrio
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to system_stm32f4xx.o(.constdata) for APBPrescTable
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to system_stm32f4xx.o(.constdata) for APBPrescTable
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) refers to lludivv7m.o(.text) for __aeabi_uldivmod
    stm32f4xx_hal_rcc.o(i.HAL_RCC_MCOConfig) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32f4xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_CSSCallback) for HAL_RCC_CSSCallback
    stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisablePLLI2S) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnablePLLI2S) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to stm32f4xx_hal.o(.data) for uwTickPrio
    stm32f4xx_hal_flash.o(i.FLASH_SetErrorCode) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f4xx_hal_flash.o(i.FLASH_SetErrorCode) for FLASH_SetErrorCode
    stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash.o(i.HAL_FLASH_GetError) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(i.FLASH_SetErrorCode) for FLASH_SetErrorCode
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback) for HAL_FLASH_OperationErrorCallback
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback) for HAL_FLASH_EndOfOperationCallback
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_Erase_Sector) for FLASH_Erase_Sector
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_FlushCaches) for FLASH_FlushCaches
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash.o(i.HAL_FLASH_OB_Launch) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_DoubleWord) for FLASH_Program_DoubleWord
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_Byte) for FLASH_Program_Byte
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_HalfWord) for FLASH_Program_HalfWord
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_Word) for FLASH_Program_Word
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_DoubleWord) for FLASH_Program_DoubleWord
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_Byte) for FLASH_Program_Byte
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_HalfWord) for FLASH_Program_HalfWord
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_Word) for FLASH_Program_Word
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_Erase_Sector) for FLASH_Erase_Sector
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_FlushCaches) for FLASH_FlushCaches
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_Erase_Sector) for FLASH_Erase_Sector
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback) for HAL_GPIO_EXTI_Callback
    stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart) refers to stm32f4xx_hal_dma_ex.o(i.DMA_MultiBufferSetConfig) for DMA_MultiBufferSetConfig
    stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart_IT) refers to stm32f4xx_hal_dma_ex.o(i.DMA_MultiBufferSetConfig) for DMA_MultiBufferSetConfig
    stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift) refers to stm32f4xx_hal_dma.o(.constdata) for .constdata
    stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(i.HAL_DMA_DeInit) refers to stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift) for DMA_CalcBaseAndBitshift
    stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32f4xx_hal_dma.o(i.DMA_CheckFifoParam) for DMA_CheckFifoParam
    stm32f4xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift) for DMA_CalcBaseAndBitshift
    stm32f4xx_hal_dma.o(i.HAL_DMA_PollForTransfer) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(i.HAL_DMA_PollForTransfer) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_dma.o(i.HAL_DMA_Start) refers to stm32f4xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) refers to stm32f4xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32f4xx_hal_pwr.o(i.HAL_PWR_PVD_IRQHandler) refers to stm32f4xx_hal_pwr.o(i.HAL_PWR_PVDCallback) for HAL_PWR_PVDCallback
    stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_ControlVoltageScaling) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableBkUpReg) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableBkUpReg) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) refers to stm32f4xx_hal_cortex.o(i.__NVIC_SetPriority) for __NVIC_SetPriority
    stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config) refers to stm32f4xx_hal_cortex.o(i.__NVIC_SetPriority) for __NVIC_SetPriority
    stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler) refers to stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Callback) for HAL_SYSTICK_Callback
    stm32f4xx_hal.o(i.HAL_DeInit) refers to stm32f4xx_hal.o(i.HAL_MspDeInit) for HAL_MspDeInit
    stm32f4xx_hal.o(i.HAL_Delay) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal.o(i.HAL_Delay) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_GetTick) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_GetTickFreq) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_GetTickPrio) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_IncTick) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_Init) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping) for HAL_NVIC_SetPriorityGrouping
    stm32f4xx_hal.o(i.HAL_Init) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal.o(i.HAL_Init) refers to stm32f4xx_hal_msp.o(i.HAL_MspInit) for HAL_MspInit
    stm32f4xx_hal.o(i.HAL_InitTick) refers to stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config) for HAL_SYSTICK_Config
    stm32f4xx_hal.o(i.HAL_InitTick) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    stm32f4xx_hal.o(i.HAL_InitTick) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_InitTick) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal.o(i.HAL_SetTickFreq) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal.o(i.HAL_SetTickFreq) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_DeInit) refers to tim.o(i.HAL_TIM_Base_MspDeInit) for HAL_TIM_Base_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init) refers to tim.o(i.HAL_TIM_Base_MspInit) for HAL_TIM_Base_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage) for TIM_TI1_ConfigInputStage
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage) for TIM_TI2_ConfigInputStage
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f4xx_hal_tim.o(i.TIM_ITRx_SetConfig) for TIM_ITRx_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigOCrefClear) refers to stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMATriggerCplt) for TIM_DMATriggerCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) for TIM_DMATriggerHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMATriggerCplt) for TIM_DMATriggerCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) for TIM_DMATriggerHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) for HAL_TIM_DMABurst_MultiReadStart
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStop) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) for HAL_TIM_DMABurst_MultiWriteStart
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStop) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_DeInit) refers to tim.o(i.HAL_TIM_Encoder_MspDeInit) for HAL_TIM_Encoder_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init) refers to tim.o(i.HAL_TIM_Encoder_MspInit) for HAL_TIM_Encoder_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI2_SetConfig) for TIM_TI2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_MspDeInit) for HAL_TIM_IC_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_MspInit) for HAL_TIM_IC_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback) for HAL_TIM_IC_CaptureCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback) for HAL_TIM_OC_DelayElapsedCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedCallback) for HAL_TIM_PeriodElapsedCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback) for HAL_TIMEx_BreakCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerCallback) for HAL_TIM_TriggerCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback) for HAL_TIMEx_CommutCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC3_SetConfig) for TIM_OC3_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC4_SetConfig) for TIM_OC4_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OC_MspDeInit) for HAL_TIM_OC_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OC_MspInit) for HAL_TIM_OC_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI2_SetConfig) for TIM_TI2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspDeInit) for HAL_TIM_OnePulse_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspInit) for HAL_TIM_OnePulse_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC3_SetConfig) for TIM_OC3_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC4_SetConfig) for TIM_OC4_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_MspDeInit) for HAL_TIM_PWM_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_MspInit) for HAL_TIM_PWM_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro) refers to stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) for TIM_SlaveTimer_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro_IT) refers to stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) for TIM_SlaveTimer_SetConfig
    stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback) for HAL_TIM_IC_CaptureCallback
    stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureHalfCpltCallback) for HAL_TIM_IC_CaptureHalfCpltCallback
    stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedHalfCpltCallback) for HAL_TIM_PWM_PulseFinishedHalfCpltCallback
    stm32f4xx_hal_tim.o(i.TIM_DMAError) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_ErrorCallback) for HAL_TIM_ErrorCallback
    stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedCallback) for HAL_TIM_PeriodElapsedCallback
    stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedHalfCpltCallback) for HAL_TIM_PeriodElapsedHalfCpltCallback
    stm32f4xx_hal_tim.o(i.TIM_DMATriggerCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerCallback) for HAL_TIM_TriggerCallback
    stm32f4xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerHalfCpltCallback) for HAL_TIM_TriggerHalfCpltCallback
    stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage) for TIM_TI1_ConfigInputStage
    stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32f4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage) for TIM_TI2_ConfigInputStage
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_DeInit) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspDeInit) for HAL_TIMEx_HallSensor_MspDeInit
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspInit) for HAL_TIMEx_HallSensor_MspInit
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) for TIM_DMADelayPulseNCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) for TIM_DMAErrorCCxN
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) for TIM_DMADelayPulseNCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) for TIM_DMAErrorCCxN
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback) for HAL_TIMEx_CommutCallback
    stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutHalfCpltCallback) for HAL_TIMEx_CommutHalfCpltCallback
    stm32f4xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f4xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_ErrorCallback) for HAL_TIM_ErrorCallback
    stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_LIN_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_LIN_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA) refers to stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) for UART_Start_Receive_DMA
    stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_IT) refers to stm32f4xx_hal_uart.o(i.UART_Start_Receive_IT) for UART_Start_Receive_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) for UART_DMARxOnlyAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) for UART_DMATxOnlyAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMATxAbortCallback) for UART_DMATxAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMARxAbortCallback) for UART_DMARxAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f4xx_hal_uart.o(i.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f4xx_hal_uart.o(i.HAL_UART_DeInit) refers to usart.o(i.HAL_UART_MspDeInit) for HAL_UART_MspDeInit
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_Receive_IT) for UART_Receive_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to usart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError) for UART_DMAAbortOnError
    stm32f4xx_hal_uart.o(i.HAL_UART_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_UART_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) for UART_Start_Receive_DMA
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT) refers to stm32f4xx_hal_uart.o(i.UART_Start_Receive_IT) for UART_Start_Receive_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMATransmitCplt) for UART_DMATransmitCplt
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMATxHalfCplt) for UART_DMATxHalfCplt
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError) refers to stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(i.UART_DMAError) refers to stm32f4xx_hal_uart.o(i.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32f4xx_hal_uart.o(i.UART_DMAError) refers to stm32f4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f4xx_hal_uart.o(i.UART_DMAError) refers to stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt) refers to usart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback) for HAL_UART_RxHalfCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt) refers to usart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATransmitCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATxAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATxHalfCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback) for HAL_UART_TxHalfCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32f4xx_hal_uart.o(i.UART_Receive_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32f4xx_hal_uart.o(i.UART_Receive_IT) refers to usart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(i.UART_SetConfig) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    stm32f4xx_hal_uart.o(i.UART_SetConfig) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32f4xx_hal_uart.o(i.UART_SetConfig) refers to lludivv7m.o(.text) for __aeabi_uldivmod
    stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt) for UART_DMAReceiveCplt
    stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt) for UART_DMARxHalfCplt
    stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) refers to stm32f4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    system_stm32f4xx.o(i.SystemCoreClockUpdate) refers to system_stm32f4xx.o(.data) for .data
    system_stm32f4xx.o(i.SystemCoreClockUpdate) refers to system_stm32f4xx.o(.constdata) for .constdata
    oled.o(i.OLED_Allfill) refers to oled.o(i.OLED_Write_cmd) for OLED_Write_cmd
    oled.o(i.OLED_Allfill) refers to oled.o(i.OLED_Write_data) for OLED_Write_data
    oled.o(i.OLED_Clear) refers to oled.o(i.OLED_Write_cmd) for OLED_Write_cmd
    oled.o(i.OLED_Clear) refers to oled.o(i.OLED_Write_data) for OLED_Write_data
    oled.o(i.OLED_Display_Off) refers to oled.o(i.OLED_Write_cmd) for OLED_Write_cmd
    oled.o(i.OLED_Display_On) refers to oled.o(i.OLED_Write_cmd) for OLED_Write_cmd
    oled.o(i.OLED_Init) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_Write_cmd) for OLED_Write_cmd
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_Clear) for OLED_Clear
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_Set_Position) for OLED_Set_Position
    oled.o(i.OLED_Init) refers to oled.o(.data) for .data
    oled.o(i.OLED_Set_Position) refers to oled.o(i.OLED_Write_cmd) for OLED_Write_cmd
    oled.o(i.OLED_ShowChar) refers to oled.o(i.OLED_Set_Position) for OLED_Set_Position
    oled.o(i.OLED_ShowChar) refers to oled.o(i.OLED_Write_data) for OLED_Write_data
    oled.o(i.OLED_ShowChar) refers to oled.o(.constdata) for .constdata
    oled.o(i.OLED_ShowFloat) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowHanzi) refers to oled.o(i.OLED_Set_Position) for OLED_Set_Position
    oled.o(i.OLED_ShowHanzi) refers to oled.o(i.OLED_Write_data) for OLED_Write_data
    oled.o(i.OLED_ShowHanzi) refers to oled.o(.constdata) for .constdata
    oled.o(i.OLED_ShowHzbig) refers to oled.o(i.OLED_Set_Position) for OLED_Set_Position
    oled.o(i.OLED_ShowHzbig) refers to oled.o(i.OLED_Write_data) for OLED_Write_data
    oled.o(i.OLED_ShowHzbig) refers to oled.o(.constdata) for .constdata
    oled.o(i.OLED_ShowNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowPic) refers to oled.o(i.OLED_Set_Position) for OLED_Set_Position
    oled.o(i.OLED_ShowPic) refers to oled.o(i.OLED_Write_data) for OLED_Write_data
    oled.o(i.OLED_ShowStr) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_Write_cmd) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) for HAL_I2C_Mem_Write
    oled.o(i.OLED_Write_cmd) refers to i2c.o(.bss) for hi2c2
    oled.o(i.OLED_Write_data) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) for HAL_I2C_Mem_Write
    oled.o(i.OLED_Write_data) refers to i2c.o(.bss) for hi2c2
    ringbuffer.o(i.rt_ringbuffer_data_len) refers to ringbuffer.o(i.rt_ringbuffer_status) for rt_ringbuffer_status
    ringbuffer.o(i.rt_ringbuffer_get) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    ringbuffer.o(i.rt_ringbuffer_get) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    ringbuffer.o(i.rt_ringbuffer_get) refers to abort.o(.text) for abort
    ringbuffer.o(i.rt_ringbuffer_getchar) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    ringbuffer.o(i.rt_ringbuffer_getchar) refers to abort.o(.text) for abort
    ringbuffer.o(i.rt_ringbuffer_init) refers to abort.o(.text) for abort
    ringbuffer.o(i.rt_ringbuffer_peek) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    ringbuffer.o(i.rt_ringbuffer_peek) refers to abort.o(.text) for abort
    ringbuffer.o(i.rt_ringbuffer_put) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    ringbuffer.o(i.rt_ringbuffer_put) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    ringbuffer.o(i.rt_ringbuffer_put) refers to abort.o(.text) for abort
    ringbuffer.o(i.rt_ringbuffer_put_force) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    ringbuffer.o(i.rt_ringbuffer_put_force) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    ringbuffer.o(i.rt_ringbuffer_put_force) refers to abort.o(.text) for abort
    ringbuffer.o(i.rt_ringbuffer_putchar) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    ringbuffer.o(i.rt_ringbuffer_putchar) refers to abort.o(.text) for abort
    ringbuffer.o(i.rt_ringbuffer_putchar_force) refers to ringbuffer.o(i.rt_ringbuffer_status) for rt_ringbuffer_status
    ringbuffer.o(i.rt_ringbuffer_putchar_force) refers to abort.o(.text) for abort
    ringbuffer.o(i.rt_ringbuffer_reset) refers to abort.o(.text) for abort
    schedule.o(i.schedule_init) refers to schedule.o(.data) for .data
    schedule.o(i.schedule_run) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    schedule.o(i.schedule_run) refers to schedule.o(.data) for .data
    schedule.o(.data) refers to uart_bsp.o(i.uart_proc) for uart_proc
    schedule.o(.data) refers to pi_bsp.o(i.pi_proc) for pi_proc
    oled_bsp.o(i.Oled_Printf) refers to vsnprintf.o(.text) for vsnprintf
    oled_bsp.o(i.Oled_Printf) refers to oled.o(i.OLED_ShowStr) for OLED_ShowStr
    key_bsp.o(i.key_proc) refers to key_bsp.o(i.key_read) for key_read
    key_bsp.o(i.key_proc) refers to key_bsp.o(.data) for .data
    key_bsp.o(i.key_read) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    motor_bsp.o(i.Location_Speed_control) refers to mypid.o(i.pid_calc) for pid_calc
    motor_bsp.o(i.Location_Speed_control) refers to motor_driver_tb6612.o(i.Motor_SetSpeed) for Motor_SetSpeed
    motor_bsp.o(i.Location_Speed_control) refers to mypid.o(.bss) for pid_location_left
    motor_bsp.o(i.Location_Speed_control) refers to encoder_bsp.o(.bss) for left_encoder
    motor_bsp.o(i.Location_Speed_control) refers to motor_bsp.o(.data) for .data
    motor_bsp.o(i.Location_Speed_control) refers to motor_bsp.o(.bss) for .bss
    motor_bsp.o(i.motor_init) refers to motor_driver_tb6612.o(i.Motor_Create) for Motor_Create
    motor_bsp.o(i.motor_init) refers to tim.o(.bss) for htim1
    motor_bsp.o(i.motor_init) refers to motor_bsp.o(.bss) for .bss
    motor_bsp.o(i.motor_proc) refers to motor_bsp.o(i.Location_Speed_control) for Location_Speed_control
    encoder_bsp.o(i.encoder_init) refers to encoder_drv.o(i.Encoder_Driver_Init) for Encoder_Driver_Init
    encoder_bsp.o(i.encoder_init) refers to tim.o(.bss) for htim3
    encoder_bsp.o(i.encoder_init) refers to encoder_bsp.o(.bss) for .bss
    encoder_bsp.o(i.encoder_proc) refers to encoder_drv.o(i.Encoder_Driver_Update) for Encoder_Driver_Update
    encoder_bsp.o(i.encoder_proc) refers to encoder_bsp.o(.bss) for .bss
    hwt101_bsp.o(i.hwt101_init) refers to hwt101_driver.o(i.HWT101_Create) for HWT101_Create
    hwt101_bsp.o(i.hwt101_init) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Receive_DMA) for HAL_UART_Receive_DMA
    hwt101_bsp.o(i.hwt101_init) refers to usart.o(.bss) for huart3
    hwt101_bsp.o(i.hwt101_init) refers to hwt101_bsp.o(.bss) for .bss
    hwt101_bsp.o(i.hwt101_init) refers to usart.o(.bss) for uart3_rx_buffer
    hwt101_bsp.o(i.hwt101_proc) refers to hwt101_driver.o(i.HWT101_GetYaw) for HWT101_GetYaw
    hwt101_bsp.o(i.hwt101_proc) refers to hwt101_driver.o(i.HWT101_GetGyroZ) for HWT101_GetGyroZ
    hwt101_bsp.o(i.hwt101_proc) refers to hwt101_driver.o(i.HWT101_GetData) for HWT101_GetData
    hwt101_bsp.o(i.hwt101_proc) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    hwt101_bsp.o(i.hwt101_proc) refers to uart_bsp.o(i.my_printf) for my_printf
    hwt101_bsp.o(i.hwt101_proc) refers to hwt101_bsp.o(.bss) for .bss
    hwt101_bsp.o(i.hwt101_proc) refers to usart.o(.bss) for huart1
    uart_bsp.o(i.check_motor_angle_limits) refers to uart_bsp.o(i.my_printf) for my_printf
    uart_bsp.o(i.check_motor_angle_limits) refers to emm_v5.o(i.Emm_V5_Stop_Now) for Emm_V5_Stop_Now
    uart_bsp.o(i.check_motor_angle_limits) refers to uart_bsp.o(.data) for .data
    uart_bsp.o(i.check_motor_angle_limits) refers to usart.o(.bss) for huart1
    uart_bsp.o(i.my_printf) refers to vsnprintf.o(.text) for vsnprintf
    uart_bsp.o(i.my_printf) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    uart_bsp.o(i.parse_x_motor_data) refers to uart_bsp.o(i.calc_motor_angle) for calc_motor_angle
    uart_bsp.o(i.parse_x_motor_data) refers to uart_bsp.o(i.calc_relative_angle) for calc_relative_angle
    uart_bsp.o(i.parse_x_motor_data) refers to uart_bsp.o(i.my_printf) for my_printf
    uart_bsp.o(i.parse_x_motor_data) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    uart_bsp.o(i.parse_x_motor_data) refers to uart_bsp.o(.data) for .data
    uart_bsp.o(i.parse_x_motor_data) refers to usart.o(.bss) for huart1
    uart_bsp.o(i.parse_x_motor_data) refers to uart_bsp.o(.conststring) for .conststring
    uart_bsp.o(i.parse_y_motor_data) refers to uart_bsp.o(i.calc_motor_angle) for calc_motor_angle
    uart_bsp.o(i.parse_y_motor_data) refers to uart_bsp.o(i.calc_relative_angle) for calc_relative_angle
    uart_bsp.o(i.parse_y_motor_data) refers to uart_bsp.o(i.my_printf) for my_printf
    uart_bsp.o(i.parse_y_motor_data) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    uart_bsp.o(i.parse_y_motor_data) refers to uart_bsp.o(.data) for .data
    uart_bsp.o(i.parse_y_motor_data) refers to usart.o(.bss) for huart1
    uart_bsp.o(i.parse_y_motor_data) refers to uart_bsp.o(.conststring) for .conststring
    uart_bsp.o(i.process_command) refers to _scanf_int.o(.text) for _scanf_int
    uart_bsp.o(i.process_command) refers to strncmp.o(.text) for strncmp
    uart_bsp.o(i.process_command) refers to __0sscanf.o(.text) for __0sscanf
    uart_bsp.o(i.process_command) refers to uart_bsp.o(i.my_printf) for my_printf
    uart_bsp.o(i.process_command) refers to uart_bsp.o(i.process_reset_command) for process_reset_command
    uart_bsp.o(i.process_command) refers to usart.o(.bss) for huart1
    uart_bsp.o(i.process_reset_command) refers to uart_bsp.o(i.my_printf) for my_printf
    uart_bsp.o(i.process_reset_command) refers to emm_v5.o(i.Emm_V5_Pos_Control) for Emm_V5_Pos_Control
    uart_bsp.o(i.process_reset_command) refers to uart_bsp.o(.data) for .data
    uart_bsp.o(i.process_reset_command) refers to usart.o(.bss) for huart1
    uart_bsp.o(i.save_initial_position) refers to emm_v5.o(i.Emm_V5_Read_Sys_Params) for Emm_V5_Read_Sys_Params
    uart_bsp.o(i.save_initial_position) refers to uart_bsp.o(i.my_printf) for my_printf
    uart_bsp.o(i.save_initial_position) refers to uart_bsp.o(.data) for .data
    uart_bsp.o(i.save_initial_position) refers to usart.o(.bss) for huart2
    uart_bsp.o(i.uart_proc) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    uart_bsp.o(i.uart_proc) refers to ringbuffer.o(i.rt_ringbuffer_get) for rt_ringbuffer_get
    uart_bsp.o(i.uart_proc) refers to emm_v5.o(i.Emm_V5_Parse_Response) for Emm_V5_Parse_Response
    uart_bsp.o(i.uart_proc) refers to uart_bsp.o(i.parse_x_motor_data) for parse_x_motor_data
    uart_bsp.o(i.uart_proc) refers to uart_bsp.o(i.my_printf) for my_printf
    uart_bsp.o(i.uart_proc) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    uart_bsp.o(i.uart_proc) refers to uart_bsp.o(i.parse_y_motor_data) for parse_y_motor_data
    uart_bsp.o(i.uart_proc) refers to pi_bsp.o(i.pi_parse_data) for pi_parse_data
    uart_bsp.o(i.uart_proc) refers to uart_bsp.o(.bss) for .bss
    uart_bsp.o(i.uart_proc) refers to usart.o(.bss) for huart1
    uart_bsp.o(i.uart_proc) refers to uart_bsp.o(.data) for .data
    step_motor_bsp.o(i.Step_Motor_Init) refers to emm_v5.o(i.Emm_V5_En_Control) for Emm_V5_En_Control
    step_motor_bsp.o(i.Step_Motor_Init) refers to step_motor_bsp.o(i.Step_Motor_Stop) for Step_Motor_Stop
    step_motor_bsp.o(i.Step_Motor_Init) refers to usart.o(.bss) for huart2
    step_motor_bsp.o(i.Step_Motor_Set_Pwm) refers to emm_v5.o(i.Emm_V5_Pos_Control) for Emm_V5_Pos_Control
    step_motor_bsp.o(i.Step_Motor_Set_Pwm) refers to usart.o(.bss) for huart2
    step_motor_bsp.o(i.Step_Motor_Set_Speed) refers to uart_bsp.o(i.my_printf) for my_printf
    step_motor_bsp.o(i.Step_Motor_Set_Speed) refers to emm_v5.o(i.Emm_V5_Vel_Control) for Emm_V5_Vel_Control
    step_motor_bsp.o(i.Step_Motor_Set_Speed) refers to usart.o(.bss) for huart1
    step_motor_bsp.o(i.Step_Motor_Set_Speed_my) refers to emm_v5.o(i.Emm_V5_Vel_Control) for Emm_V5_Vel_Control
    step_motor_bsp.o(i.Step_Motor_Set_Speed_my) refers to usart.o(.bss) for huart2
    step_motor_bsp.o(i.Step_Motor_Stop) refers to emm_v5.o(i.Emm_V5_Stop_Now) for Emm_V5_Stop_Now
    step_motor_bsp.o(i.Step_Motor_Stop) refers to usart.o(.bss) for huart2
    pi_bsp.o(i.pi_parse_data) refers to _scanf_int.o(.text) for _scanf_int
    pi_bsp.o(i.pi_parse_data) refers to strncmp.o(.text) for strncmp
    pi_bsp.o(i.pi_parse_data) refers to __0sscanf.o(.text) for __0sscanf
    pi_bsp.o(i.pi_parse_data) refers to uart_bsp.o(i.my_printf) for my_printf
    pi_bsp.o(i.pi_parse_data) refers to pi_bsp.o(.data) for .data
    pi_bsp.o(i.pi_parse_data) refers to usart.o(.bss) for huart1
    pi_bsp.o(i.pi_proc) refers to mypid.o(i.pid_calc) for pid_calc
    pi_bsp.o(i.pi_proc) refers to step_motor_bsp.o(i.Step_Motor_Set_Speed_my) for Step_Motor_Set_Speed_my
    pi_bsp.o(i.pi_proc) refers to pi_bsp.o(.data) for .data
    pi_bsp.o(i.pi_proc) refers to mypid.o(.bss) for pid_x
    gray_bsp.o(i.gray_proc) refers to hardware_iic.o(i.IIC_Get_Digtal) for IIC_Get_Digtal
    gray_bsp.o(i.gray_proc) refers to uart_bsp.o(i.my_printf) for my_printf
    gray_bsp.o(i.gray_proc) refers to gray_bsp.o(.data) for .data
    gray_bsp.o(i.gray_proc) refers to usart.o(.bss) for huart1
    encoder_drv.o(i.Encoder_Driver_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start) for HAL_TIM_Encoder_Start
    hwt101_driver.o(i.HWT101_Enable) refers to hwt101_driver.o(i.HWT101_ValidateParams) for HWT101_ValidateParams
    hwt101_driver.o(i.HWT101_GetData) refers to hwt101_driver.o(i.HWT101_ValidateParams) for HWT101_ValidateParams
    hwt101_driver.o(i.HWT101_GetGyroZ) refers to hwt101_driver.o(i.HWT101_ValidateParams) for HWT101_ValidateParams
    hwt101_driver.o(i.HWT101_GetState) refers to hwt101_driver.o(i.HWT101_ValidateParams) for HWT101_ValidateParams
    hwt101_driver.o(i.HWT101_GetYaw) refers to hwt101_driver.o(i.HWT101_ValidateParams) for HWT101_ValidateParams
    hwt101_driver.o(i.HWT101_ProcessBuffer) refers to hwt101_driver.o(i.HWT101_ValidateParams) for HWT101_ValidateParams
    hwt101_driver.o(i.HWT101_ProcessBuffer) refers to hwt101_driver.o(i.HWT101_ConvertGyroData) for HWT101_ConvertGyroData
    hwt101_driver.o(i.HWT101_ProcessBuffer) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    hwt101_driver.o(i.HWT101_ResetYaw) refers to hwt101_driver.o(i.HWT101_ValidateParams) for HWT101_ValidateParams
    hwt101_driver.o(i.HWT101_ResetYaw) refers to hwt101_driver.o(i.HWT101_UnlockRegister) for HWT101_UnlockRegister
    hwt101_driver.o(i.HWT101_ResetYaw) refers to hwt101_driver.o(i.HWT101_SendCommand) for HWT101_SendCommand
    hwt101_driver.o(i.HWT101_ResetYaw) refers to hwt101_driver.o(i.HWT101_SaveConfig) for HWT101_SaveConfig
    hwt101_driver.o(i.HWT101_ResetYaw) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    hwt101_driver.o(i.HWT101_SaveConfig) refers to hwt101_driver.o(i.HWT101_ValidateParams) for HWT101_ValidateParams
    hwt101_driver.o(i.HWT101_SaveConfig) refers to __2printf.o(.text) for __2printf
    hwt101_driver.o(i.HWT101_SaveConfig) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    hwt101_driver.o(i.HWT101_SendCommand) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    hwt101_driver.o(i.HWT101_SendCommand) refers to _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) for _printf_c
    hwt101_driver.o(i.HWT101_SendCommand) refers to _printf_str.o(.text) for _printf_str
    hwt101_driver.o(i.HWT101_SendCommand) refers to __2printf.o(.text) for __2printf
    hwt101_driver.o(i.HWT101_SendCommand) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    hwt101_driver.o(i.HWT101_SetBaudRate) refers to hwt101_driver.o(i.HWT101_ValidateParams) for HWT101_ValidateParams
    hwt101_driver.o(i.HWT101_SetBaudRate) refers to hwt101_driver.o(i.HWT101_UnlockRegister) for HWT101_UnlockRegister
    hwt101_driver.o(i.HWT101_SetBaudRate) refers to hwt101_driver.o(i.HWT101_SendCommand) for HWT101_SendCommand
    hwt101_driver.o(i.HWT101_SetBaudRate) refers to hwt101_driver.o(i.HWT101_SaveConfig) for HWT101_SaveConfig
    hwt101_driver.o(i.HWT101_SetBaudRate) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    hwt101_driver.o(i.HWT101_SetOutputRate) refers to hwt101_driver.o(i.HWT101_ValidateParams) for HWT101_ValidateParams
    hwt101_driver.o(i.HWT101_SetOutputRate) refers to hwt101_driver.o(i.HWT101_UnlockRegister) for HWT101_UnlockRegister
    hwt101_driver.o(i.HWT101_SetOutputRate) refers to hwt101_driver.o(i.HWT101_SendCommand) for HWT101_SendCommand
    hwt101_driver.o(i.HWT101_SetOutputRate) refers to hwt101_driver.o(i.HWT101_SaveConfig) for HWT101_SaveConfig
    hwt101_driver.o(i.HWT101_SetOutputRate) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    hwt101_driver.o(i.HWT101_StartManualCalibration) refers to hwt101_driver.o(i.HWT101_ValidateParams) for HWT101_ValidateParams
    hwt101_driver.o(i.HWT101_StartManualCalibration) refers to hwt101_driver.o(i.HWT101_UnlockRegister) for HWT101_UnlockRegister
    hwt101_driver.o(i.HWT101_StartManualCalibration) refers to hwt101_driver.o(i.HWT101_SendCommand) for HWT101_SendCommand
    hwt101_driver.o(i.HWT101_StartManualCalibration) refers to hwt101_driver.o(i.HWT101_SaveConfig) for HWT101_SaveConfig
    hwt101_driver.o(i.HWT101_StartManualCalibration) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    hwt101_driver.o(i.HWT101_StopManualCalibration) refers to hwt101_driver.o(i.HWT101_ValidateParams) for HWT101_ValidateParams
    hwt101_driver.o(i.HWT101_StopManualCalibration) refers to hwt101_driver.o(i.HWT101_UnlockRegister) for HWT101_UnlockRegister
    hwt101_driver.o(i.HWT101_StopManualCalibration) refers to hwt101_driver.o(i.HWT101_SendCommand) for HWT101_SendCommand
    hwt101_driver.o(i.HWT101_StopManualCalibration) refers to hwt101_driver.o(i.HWT101_SaveConfig) for HWT101_SaveConfig
    hwt101_driver.o(i.HWT101_StopManualCalibration) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    hwt101_driver.o(i.HWT101_UnlockRegister) refers to __2printf.o(.text) for __2printf
    hwt101_driver.o(i.HWT101_UnlockRegister) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    hardware_iic.o(i.IIC_Anolog_Normalize) refers to hardware_iic.o(i.IIC_WriteBytes) for IIC_WriteBytes
    hardware_iic.o(i.IIC_Get_Anolog) refers to hardware_iic.o(i.IIC_ReadBytes) for IIC_ReadBytes
    hardware_iic.o(i.IIC_Get_Digtal) refers to hardware_iic.o(i.IIC_ReadBytes) for IIC_ReadBytes
    hardware_iic.o(i.IIC_Get_Offset) refers to hardware_iic.o(i.IIC_ReadBytes) for IIC_ReadBytes
    hardware_iic.o(i.IIC_Get_Single_Anolog) refers to hardware_iic.o(i.IIC_ReadBytes) for IIC_ReadBytes
    hardware_iic.o(i.IIC_ReadByte) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive) for HAL_I2C_Master_Receive
    hardware_iic.o(i.IIC_ReadByte) refers to i2c.o(.bss) for hi2c1
    hardware_iic.o(i.IIC_ReadBytes) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read) for HAL_I2C_Mem_Read
    hardware_iic.o(i.IIC_ReadBytes) refers to i2c.o(.bss) for hi2c1
    hardware_iic.o(i.IIC_WriteByte) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) for HAL_I2C_Master_Transmit
    hardware_iic.o(i.IIC_WriteByte) refers to i2c.o(.bss) for hi2c1
    hardware_iic.o(i.IIC_WriteBytes) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) for HAL_I2C_Mem_Write
    hardware_iic.o(i.IIC_WriteBytes) refers to i2c.o(.bss) for hi2c1
    hardware_iic.o(i.Ping) refers to hardware_iic.o(i.IIC_ReadBytes) for IIC_ReadBytes
    emm_v5.o(i.Emm_V5_En_Control) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    emm_v5.o(i.Emm_V5_Modify_Ctrl_Mode) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    emm_v5.o(i.Emm_V5_Origin_Interrupt) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    emm_v5.o(i.Emm_V5_Origin_Modify_Params) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    emm_v5.o(i.Emm_V5_Origin_Modify_Params) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    emm_v5.o(i.Emm_V5_Origin_Set_O) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    emm_v5.o(i.Emm_V5_Origin_Trigger_Return) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    emm_v5.o(i.Emm_V5_Parse_Response) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    emm_v5.o(i.Emm_V5_Pos_Control) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    emm_v5.o(i.Emm_V5_Read_Sys_Params) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    emm_v5.o(i.Emm_V5_Reset_Clog_Pro) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    emm_v5.o(i.Emm_V5_Reset_CurPos_To_Zero) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    emm_v5.o(i.Emm_V5_Stop_Now) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    emm_v5.o(i.Emm_V5_Synchronous_motion) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    emm_v5.o(i.Emm_V5_Vel_Control) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    emm_v5.o(i.Emm_V5_Vel_Control) refers to uart_bsp.o(i.my_printf) for my_printf
    emm_v5.o(i.Emm_V5_Vel_Control) refers to usart.o(.bss) for huart1
    mypid.o(i.PID_INIT) refers to mypid.o(i.PID_struct_init) for PID_struct_init
    mypid.o(i.PID_INIT) refers to mypid.o(.bss) for .bss
    mypid.o(i.PID_struct_init) refers to mypid.o(i.pid_param_init) for pid_param_init
    mypid.o(i.PID_struct_init) refers to mypid.o(i.pid_reset) for pid_reset
    mypid.o(i.pid_angle_calc) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    mypid.o(i.pid_angle_calc) refers to fmod.o(i.__hardfp_fmod) for __hardfp_fmod
    mypid.o(i.pid_angle_calc) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    mypid.o(i.pid_angle_calc) refers to mypid.o(i.abs_limit) for abs_limit
    mypid.o(i.pid_calc) refers to mypid.o(i.abs_limit) for abs_limit
    mypid.o(i.pid_calc_d) refers to mypid.o(i.abs_limit) for abs_limit
    mypid.o(i.pid_calc_i_separation) refers to mypid.o(i.abs_limit) for abs_limit
    mypid.o(i.pid_yaw_calc) refers to mypid.o(i.abs_limit) for abs_limit
    motor_driver_tb6612.o(i.Motor_Create) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start) for HAL_TIM_PWM_Start
    motor_driver_tb6612.o(i.Motor_Create) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    motor_driver_tb6612.o(i.Motor_GetState) refers to motor_driver_tb6612.o(i.Motor_ValidateParams) for Motor_ValidateParams
    motor_driver_tb6612.o(i.Motor_SetEnable) refers to motor_driver_tb6612.o(i.Motor_ValidateParams) for Motor_ValidateParams
    motor_driver_tb6612.o(i.Motor_SetEnable) refers to motor_driver_tb6612.o(i.Motor_Stop) for Motor_Stop
    motor_driver_tb6612.o(i.Motor_SetSpeed) refers to motor_driver_tb6612.o(i.Motor_ValidateParams) for Motor_ValidateParams
    motor_driver_tb6612.o(i.Motor_SetSpeed) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    motor_driver_tb6612.o(i.Motor_SetSpeed) refers to motor_driver_tb6612.o(i.Speed_To_PWM) for Speed_To_PWM
    motor_driver_tb6612.o(i.Motor_Stop) refers to motor_driver_tb6612.o(i.Motor_SetSpeed) for Motor_SetSpeed
    motor_driver_tb6612.o(i.TB6612_Init) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    vsnprintf.o(.text) refers (Special) to _printf_a.o(.ARM.Collect$$_printf_percent$$00000006) for _printf_a
    vsnprintf.o(.text) refers (Special) to _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) for _printf_c
    vsnprintf.o(.text) refers (Special) to _printf_charcount.o(.text) for _printf_charcount
    vsnprintf.o(.text) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    vsnprintf.o(.text) refers (Special) to _printf_e.o(.ARM.Collect$$_printf_percent$$00000004) for _printf_e
    vsnprintf.o(.text) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    vsnprintf.o(.text) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    vsnprintf.o(.text) refers (Special) to printf2.o(x$fpl$printf2) for _printf_fp_hex
    vsnprintf.o(.text) refers (Special) to _printf_g.o(.ARM.Collect$$_printf_percent$$00000005) for _printf_g
    vsnprintf.o(.text) refers (Special) to _printf_i.o(.ARM.Collect$$_printf_percent$$00000008) for _printf_i
    vsnprintf.o(.text) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    vsnprintf.o(.text) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    vsnprintf.o(.text) refers (Special) to _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) for _printf_lc
    vsnprintf.o(.text) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    vsnprintf.o(.text) refers (Special) to _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) for _printf_lld
    vsnprintf.o(.text) refers (Special) to _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) for _printf_lli
    vsnprintf.o(.text) refers (Special) to _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) for _printf_llo
    vsnprintf.o(.text) refers (Special) to _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) for _printf_llu
    vsnprintf.o(.text) refers (Special) to _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) for _printf_llx
    vsnprintf.o(.text) refers (Special) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    vsnprintf.o(.text) refers (Special) to _printf_hex_int_ll_ptr.o(.text) for _printf_longlong_hex
    vsnprintf.o(.text) refers (Special) to _printf_oct_int_ll.o(.text) for _printf_longlong_oct
    vsnprintf.o(.text) refers (Special) to _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) for _printf_ls
    vsnprintf.o(.text) refers (Special) to _printf_n.o(.ARM.Collect$$_printf_percent$$00000001) for _printf_n
    vsnprintf.o(.text) refers (Special) to _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B) for _printf_o
    vsnprintf.o(.text) refers (Special) to _printf_p.o(.ARM.Collect$$_printf_percent$$00000002) for _printf_p
    vsnprintf.o(.text) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    vsnprintf.o(.text) refers (Special) to _printf_pad.o(.text) for _printf_post_padding
    vsnprintf.o(.text) refers (Special) to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    vsnprintf.o(.text) refers (Special) to _printf_str.o(.text) for _printf_str
    vsnprintf.o(.text) refers (Special) to _printf_truncate.o(.text) for _printf_truncate_signed
    vsnprintf.o(.text) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    vsnprintf.o(.text) refers (Special) to _printf_wctomb.o(.text) for _printf_wctomb
    vsnprintf.o(.text) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    vsnprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    vsnprintf.o(.text) refers to _sputc.o(.text) for _sputc
    vsnprintf.o(.text) refers to _snputc.o(.text) for _snputc
    __2printf.o(.text) refers to _printf_char_file.o(.text) for _printf_char_file
    __2printf.o(.text) refers to stdio_streams.o(.bss) for __stdout
    noretval__2printf.o(.text) refers to _printf_char_file.o(.text) for _printf_char_file
    noretval__2printf.o(.text) refers to stdio_streams.o(.bss) for __stdout
    __printf.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    _printf_str.o(.text) refers (Special) to _printf_char.o(.text) for _printf_cs_common
    _printf_str.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_str.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    __printf_flags.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags.o(.text) refers to __printf_flags.o(.constdata) for .constdata
    __printf_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to __printf_flags_ss.o(.constdata) for .constdata
    __printf_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_flags_wp.o(.constdata) for .constdata
    __printf_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_flags_ss_wp.o(.constdata) for .constdata
    _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) refers (Weak) to _printf_char.o(.text) for _printf_char
    _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) refers (Special) to _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017) for _printf_percent_end
    __0sscanf.o(.text) refers to scanf_char.o(.text) for __vfscanf_char
    __0sscanf.o(.text) refers to _sgetc.o(.text) for _sgetc
    _scanf_int.o(.text) refers to _chval.o(.text) for _chval
    abort.o(.text) refers to defsig_abrt_outer.o(.text) for __rt_SIGABRT
    abort.o(.text) refers (Weak) to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    abort.o(.text) refers to sys_exit.o(.text) for _sys_exit
    rt_memcpy_v6.o(.text) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    d2f.o(x$fpl$d2f) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    d2f.o(x$fpl$d2f) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    d2f.o(x$fpl$d2f) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    f2d.o(x$fpl$f2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    f2d.o(x$fpl$f2d) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    f2d.o(x$fpl$f2d) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    fmod.o(i.__hardfp_fmod) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fmod.o(i.__hardfp_fmod) refers to drem_clz.o(x$fpl$drem) for _drem
    fmod.o(i.__hardfp_fmod) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    fmod.o(i.__hardfp_fmod) refers to _rserrno.o(.text) for __set_errno
    fmod.o(i.__hardfp_fmod) refers to dunder.o(i.__mathlib_dbl_invalid) for __mathlib_dbl_invalid
    fmod.o(i.__softfp_fmod) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fmod.o(i.__softfp_fmod) refers to fmod.o(i.__hardfp_fmod) for __hardfp_fmod
    fmod.o(i.fmod) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fmod.o(i.fmod) refers to fmod.o(i.__hardfp_fmod) for __hardfp_fmod
    fmod_x.o(i.____hardfp_fmod$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fmod_x.o(i.____hardfp_fmod$lsc) refers to drem_clz.o(x$fpl$drem) for _drem
    fmod_x.o(i.____hardfp_fmod$lsc) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    fmod_x.o(i.____hardfp_fmod$lsc) refers to _rserrno.o(.text) for __set_errno
    fmod_x.o(i.____softfp_fmod$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fmod_x.o(i.____softfp_fmod$lsc) refers to drem_clz.o(x$fpl$drem) for _drem
    fmod_x.o(i.____softfp_fmod$lsc) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    fmod_x.o(i.____softfp_fmod$lsc) refers to _rserrno.o(.text) for __set_errno
    fmod_x.o(i.__fmod$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fmod_x.o(i.__fmod$lsc) refers to drem_clz.o(x$fpl$drem) for _drem
    fmod_x.o(i.__fmod$lsc) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    fmod_x.o(i.__fmod$lsc) refers to _rserrno.o(.text) for __set_errno
    sys_exit.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_exit.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    aeabi_ldiv0_sigfpe.o(.text) refers to rt_div0.o(.text) for __rt_div0
    _rserrno.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    _printf_dec.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_signed
    _printf_dec.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_char_common.o(.text) refers to __printf_flags_ss_wp.o(.text) for __printf
    _printf_char.o(.text) refers (Weak) to _printf_str.o(.text) for _printf_str
    _printf_char_file.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    _printf_char_file.o(.text) refers to ferror.o(.text) for ferror
    _printf_char_file.o(.text) refers to fputc.o(i.fputc) for fputc
    _printf_wctomb.o(.text) refers (Special) to _printf_wchar.o(.text) for _printf_lcs_common
    _printf_wctomb.o(.text) refers to _wcrtomb.o(.text) for _wcrtomb
    _printf_wctomb.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_wctomb.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_wctomb.o(.text) refers to _printf_wctomb.o(.constdata) for .constdata
    _printf_wctomb.o(.constdata) refers (Special) to _printf_wchar.o(.text) for _printf_lcs_common
    _printf_longlong_dec.o(.text) refers to lludiv10.o(.text) for _ll_udiv10
    _printf_longlong_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_oct_int.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int_ll.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll.o(.text) refers to _printf_hex_ll.o(.constdata) for .constdata
    _printf_hex_int.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int.o(.text) refers to _printf_hex_int.o(.constdata) for .constdata
    _printf_hex_int_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ll.o(.text) refers to _printf_hex_int_ll.o(.constdata) for .constdata
    _printf_hex_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ptr.o(.text) refers to _printf_hex_ptr.o(.constdata) for .constdata
    _printf_hex_int_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ptr.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ptr.o(.text) refers to _printf_hex_int_ptr.o(.constdata) for .constdata
    _printf_hex_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll_ptr.o(.text) refers to _printf_hex_ll_ptr.o(.constdata) for .constdata
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll_ptr.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_hex_int_ll_ptr.o(.constdata) for .constdata
    _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) refers (Weak) to _printf_char.o(.text) for _printf_string
    _printf_n.o(.ARM.Collect$$_printf_percent$$00000001) refers (Weak) to _printf_charcount.o(.text) for _printf_charcount
    _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_int_hex
    _printf_p.o(.ARM.Collect$$_printf_percent$$00000002) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_hex_ptr
    _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B) refers (Weak) to _printf_oct_int_ll.o(.text) for _printf_int_oct
    _printf_i.o(.ARM.Collect$$_printf_percent$$00000008) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_e.o(.ARM.Collect$$_printf_percent$$00000004) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_g.o(.ARM.Collect$$_printf_percent$$00000005) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_a.o(.ARM.Collect$$_printf_percent$$00000006) refers (Weak) to printf2.o(x$fpl$printf2) for _printf_fp_hex
    _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) refers (Weak) to _printf_wchar.o(.text) for _printf_wchar
    _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) refers (Weak) to _printf_wchar.o(.text) for _printf_wstring
    _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) refers (Weak) to _printf_oct_int_ll.o(.text) for _printf_ll_oct
    _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_ll_hex
    scanf_char.o(.text) refers to _scanf.o(.text) for __vfscanf
    scanf_char.o(.text) refers to isspace.o(.text) for isspace
    stdio_streams.o(.bss) refers (Special) to initio.o(.text) for _initio
    stdio_streams.o(.bss) refers (Special) to initio.o(.text) for _initio
    stdio_streams.o(.bss) refers (Special) to initio.o(.text) for _initio
    stdio_streams.o(.data) refers (Special) to initio.o(.text) for _initio
    stdio_streams.o(.data) refers (Special) to initio.o(.text) for _initio
    stdio_streams.o(.data) refers (Special) to initio.o(.text) for _initio
    defsig_abrt_outer.o(.text) refers to defsig_abrt_inner.o(.text) for __rt_SIGABRT_inner
    defsig_abrt_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_abrt_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    daddsub_clz.o(x$fpl$dadd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$dadd) refers to daddsub_clz.o(x$fpl$dsub) for _dsub1
    daddsub_clz.o(x$fpl$dadd) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    daddsub_clz.o(x$fpl$dadd) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    daddsub_clz.o(x$fpl$drsb) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$drsb) refers to daddsub_clz.o(x$fpl$dadd) for _dadd1
    daddsub_clz.o(x$fpl$drsb) refers to daddsub_clz.o(x$fpl$dsub) for _dsub1
    daddsub_clz.o(x$fpl$dsub) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$dsub) refers to daddsub_clz.o(x$fpl$dadd) for _dadd1
    daddsub_clz.o(x$fpl$dsub) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dnaninf.o(x$fpl$dnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    drem_clz.o(x$fpl$drem) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    drem_clz.o(x$fpl$drem) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dretinf.o(x$fpl$dretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fnaninf.o(x$fpl$fnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fretinf.o(x$fpl$fretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    printf1.o(x$fpl$printf1) refers to _printf_fp_dec.o(.text) for _printf_fp_dec_real
    printf2.o(x$fpl$printf2) refers to _printf_fp_hex.o(.text) for _printf_fp_hex_real
    printf2b.o(x$fpl$printf2) refers to _printf_fp_hex.o(.text) for _printf_fp_hex_real
    dunder.o(i.__mathlib_dbl_divzero) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_infnan) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_infnan2) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_invalid) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_overflow) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_posinfnan) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_underflow) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to main.o(i.main) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    rt_div0.o(.text) refers to defsig_fpe_outer.o(.text) for __rt_SIGFPE
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to sys_exit.o(.text) for _sys_exit
    rt_errno_addr.o(.text) refers to rt_errno_addr.o(.bss) for __aeabi_errno_addr_data
    rt_errno_addr_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    isspace.o(.text) refers to rt_ctype_table.o(.text) for __rt_ctype_table
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_dec.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_dec.o(.text) refers (Special) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    _printf_fp_dec.o(.text) refers to bigflt0.o(.text) for _btod_etento
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_d2e) for _btod_d2e
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    _printf_fp_dec.o(.text) refers to lludiv10.o(.text) for _ll_udiv10
    _printf_fp_dec.o(.text) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    _printf_fp_dec.o(.text) refers to _printf_fp_infnan.o(.text) for _printf_fp_infnan
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_dec.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_hex.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_hex.o(.text) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    _printf_fp_hex.o(.text) refers to _printf_fp_infnan.o(.text) for _printf_fp_infnan
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_hex.o(.text) refers to _printf_fp_hex.o(.constdata) for .constdata
    _printf_fp_hex.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_wchar.o(.text) refers (Weak) to _printf_wctomb.o(.text) for _printf_wctomb
    _scanf.o(.text) refers (Weak) to _scanf_int.o(.text) for _scanf_int
    fputc.o(i.fputc) refers to flsbuf.o(.text) for __flsbuf_byte
    initio.o(.text) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000024) for __rt_lib_init_stdio_2
    initio.o(.text) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000005) for __rt_lib_shutdown_stdio_2
    initio.o(.text) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    initio.o(.text) refers to fopen.o(.text) for freopen
    initio.o(.text) refers to defsig_rtred_outer.o(.text) for __rt_SIGRTRED
    initio.o(.text) refers to setvbuf.o(.text) for setvbuf
    initio.o(.text) refers to fclose.o(.text) for _fclose_internal
    initio.o(.text) refers to h1_free.o(.text) for free
    initio.o(.text) refers to stdio_streams.o(.bss) for __stdin
    initio.o(.text) refers to stdio_streams.o(.bss) for __stdout
    initio.o(.text) refers to stdio_streams.o(.bss) for __stderr
    initio.o(.text) refers to stdio_streams.o(.data) for __aeabi_stdin
    initio.o(.text) refers to stdio_streams.o(.data) for __aeabi_stdout
    initio.o(.text) refers to stdio_streams.o(.data) for __aeabi_stderr
    initio.o(.text) refers to sys_io.o(.constdata) for __stdin_name
    initio.o(.text) refers to sys_io.o(.constdata) for __stdout_name
    initio.o(.text) refers to sys_io.o(.constdata) for __stderr_name
    initio_locked.o(.text) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000024) for __rt_lib_init_stdio_2
    initio_locked.o(.text) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000005) for __rt_lib_shutdown_stdio_2
    initio_locked.o(.text) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    initio_locked.o(.text) refers to fopen.o(.text) for freopen
    initio_locked.o(.text) refers to defsig_rtred_outer.o(.text) for __rt_SIGRTRED
    initio_locked.o(.text) refers to setvbuf.o(.text) for setvbuf
    initio_locked.o(.text) refers to fclose.o(.text) for _fclose_internal
    initio_locked.o(.text) refers to h1_free.o(.text) for free
    initio_locked.o(.text) refers to stdio_streams.o(.bss) for __stdin
    initio_locked.o(.text) refers to stdio_streams.o(.bss) for __stdout
    initio_locked.o(.text) refers to stdio_streams.o(.bss) for __stderr
    initio_locked.o(.text) refers to stdio_streams.o(.data) for __aeabi_stdin
    initio_locked.o(.text) refers to stdio_streams.o(.data) for __aeabi_stdout
    initio_locked.o(.text) refers to stdio_streams.o(.data) for __aeabi_stderr
    initio_locked.o(.text) refers to streamlock.o(.data) for _stream_list_lock
    initio_locked.o(.text) refers to sys_io.o(.constdata) for __stdin_name
    initio_locked.o(.text) refers to sys_io.o(.constdata) for __stdout_name
    initio_locked.o(.text) refers to sys_io.o(.constdata) for __stderr_name
    _wcrtomb.o(.text) refers to rt_ctype_table.o(.text) for __rt_ctype_table
    defsig_exit.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    ddiv.o(x$fpl$drdiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$drdiv) refers to ddiv.o(x$fpl$ddiv) for ddiv_entry
    ddiv.o(x$fpl$ddiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$ddiv) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    ddiv.o(x$fpl$ddiv) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dmul.o(x$fpl$dmul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dmul.o(x$fpl$dmul) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    dmul.o(x$fpl$dmul) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    sys_io.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_io.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_io.o(.text) refers to strlen.o(.text) for strlen
    sys_io.o(.constdata) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_io.o(.constdata) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_io.o(.constdata) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_io.o(.constdata) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_io.o(.constdata) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_io.o(.constdata) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_stm32f407xx.o(.text) for __user_initial_stackheap
    rt_ctype_table.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    rt_ctype_table.o(.text) refers to lc_ctype_c.o(locale$$code) for _get_lc_ctype
    rt_locale.o(.text) refers to rt_locale.o(.bss) for __rt_locale_data
    rt_locale_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    free.o(.text) refers (Special) to hguard.o(.text) for __heap$guard
    free.o(.text) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    free.o(.text) refers to heapstubs.o(.text) for __Heap_Free
    h1_free.o(.text) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    h1_free_mt.o(.text) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2.o(i._FDIterate) refers to heap2.o(.conststring) for .conststring
    heap2.o(i.___Heap_ProvideMemory$realtime) refers to fdtree.o(i._FDTree_Delete) for _FDTree_Delete
    heap2.o(i.___Heap_ProvideMemory$realtime) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2.o(i.___Heap_Stats$realtime) refers to heap2.o(i._Heap2_StatsIterate) for _Heap2_StatsIterate
    heap2.o(i.___Heap_Valid$realtime) refers to heap2.o(i._FDIterate) for _FDIterate
    heap2.o(i.___Heap_Valid$realtime) refers to heap2.o(.conststring) for .conststring
    heap2.o(i._free$realtime) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2.o(i._free$realtime) refers to fdtree.o(i._FDTree_Delete) for _FDTree_Delete
    heap2.o(i._free$realtime) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2.o(i._malloc$realtime) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2.o(i._malloc$realtime) refers to fdtree.o(i._FDTree_FindFirst) for _FDTree_FindFirst
    heap2.o(i._malloc$realtime) refers to init_alloc.o(.text) for __Heap_Full
    heap2.o(i._malloc$realtime) refers to fdtree.o(i._FDTree_RemoveNode) for _FDTree_RemoveNode
    heap2.o(i._malloc$realtime) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2.o(i._posix_memalign$realtime) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2.o(i._posix_memalign$realtime) refers to fdtree.o(i._FDTree_FindFirst) for _FDTree_FindFirst
    heap2.o(i._posix_memalign$realtime) refers to init_alloc.o(.text) for __Heap_Full
    heap2.o(i._posix_memalign$realtime) refers to fdtree.o(i._FDTree_RemoveNode) for _FDTree_RemoveNode
    heap2.o(i._posix_memalign$realtime) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2.o(i._realloc$realtime) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2.o(i._realloc$realtime) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2.o(i._realloc$realtime) refers to h1_free.o(.text) for free
    heap2.o(i._realloc$realtime) refers to h1_alloc.o(.text) for malloc
    heap2.o(i._realloc$realtime) refers to fdtree.o(i._FDTree_Delete) for _FDTree_Delete
    heap2.o(i._realloc$realtime) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    heap2mt.o(i._FDIterate) refers to heap2mt.o(.conststring) for .conststring
    heap2mt.o(i.___Heap_Initialize$realtime$concurrent) refers to mutex_dummy.o(.text) for _mutex_initialize
    heap2mt.o(i.___Heap_ProvideMemory$realtime$concurrent) refers to fdtree.o(i._FDTree_Delete) for _FDTree_Delete
    heap2mt.o(i.___Heap_ProvideMemory$realtime$concurrent) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2mt.o(i.___Heap_Stats$realtime$concurrent) refers to heap2mt.o(i._Heap2_StatsIterate) for _Heap2_StatsIterate
    heap2mt.o(i.___Heap_Valid$realtime$concurrent) refers to heap2mt.o(i._FDIterate) for _FDIterate
    heap2mt.o(i.___Heap_Valid$realtime$concurrent) refers to heap2mt.o(.conststring) for .conststring
    heap2mt.o(i._free$realtime$concurrent) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2mt.o(i._free$realtime$concurrent) refers to fdtree.o(i._FDTree_Delete) for _FDTree_Delete
    heap2mt.o(i._free$realtime$concurrent) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2mt.o(i._malloc$realtime$concurrent) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2mt.o(i._malloc$realtime$concurrent) refers to fdtree.o(i._FDTree_FindFirst) for _FDTree_FindFirst
    heap2mt.o(i._malloc$realtime$concurrent) refers to init_alloc.o(.text) for __Heap_Full
    heap2mt.o(i._malloc$realtime$concurrent) refers to fdtree.o(i._FDTree_RemoveNode) for _FDTree_RemoveNode
    heap2mt.o(i._malloc$realtime$concurrent) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2mt.o(i._posix_memalign$realtime$concurrent) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2mt.o(i._posix_memalign$realtime$concurrent) refers to fdtree.o(i._FDTree_FindFirst) for _FDTree_FindFirst
    heap2mt.o(i._posix_memalign$realtime$concurrent) refers to init_alloc.o(.text) for __Heap_Full
    heap2mt.o(i._posix_memalign$realtime$concurrent) refers to fdtree.o(i._FDTree_RemoveNode) for _FDTree_RemoveNode
    heap2mt.o(i._posix_memalign$realtime$concurrent) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2mt.o(i._realloc$realtime$concurrent) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2mt.o(i._realloc$realtime$concurrent) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2mt.o(i._realloc$realtime$concurrent) refers to h1_free.o(.text) for free
    heap2mt.o(i._realloc$realtime$concurrent) refers to h1_alloc.o(.text) for malloc
    heap2mt.o(i._realloc$realtime$concurrent) refers to fdtree.o(i._FDTree_Delete) for _FDTree_Delete
    heap2mt.o(i._realloc$realtime$concurrent) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    _printf_fp_infnan.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_infnan.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    bigflt0.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    bigflt0.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    bigflt0.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    bigflt0.o(.text) refers to bigflt0.o(.constdata) for .constdata
    bigflt0.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers to btod.o(CL$$btod_d2e_norm_op1) for _d2e_norm_op1
    btod.o(CL$$btod_d2e_norm_op1) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e_norm_op1) refers to btod.o(CL$$btod_d2e_denorm_low) for _d2e_denorm_low
    btod.o(CL$$btod_d2e_denorm_low) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_ediv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_emuld) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_edivd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_e2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_mult_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_div_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    flsbuf.o(.text) refers to stdio.o(.text) for _deferredlazyseek
    flsbuf.o(.text) refers to sys_io.o(.text) for _sys_flen
    flsbuf.o(.text) refers to h1_alloc.o(.text) for malloc
    streamlock.o(.data) refers (Special) to initio.o(.text) for _initio
    fopen.o(.text) refers to fclose.o(.text) for _fclose_internal
    fopen.o(.text) refers to sys_io.o(.text) for _sys_open
    fopen.o(.text) refers to fseek.o(.text) for _fseek
    fopen.o(.text) refers to h1_alloc.o(.text) for malloc
    fopen.o(.text) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    fopen.o(.text) refers to stdio_streams.o(.bss) for __stdin
    fclose.o(.text) refers to stdio.o(.text) for _fflush
    fclose.o(.text) refers to sys_io.o(.text) for _sys_close
    fclose.o(.text) refers to h1_free.o(.text) for free
    fclose.o(.text) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    fopen_locked.o(.text) refers to fclose.o(.text) for _fclose_internal
    fopen_locked.o(.text) refers to sys_io.o(.text) for _sys_open
    fopen_locked.o(.text) refers to fseek.o(.text) for _fseek
    fopen_locked.o(.text) refers to h1_alloc.o(.text) for malloc
    fopen_locked.o(.text) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    fopen_locked.o(.text) refers to streamlock.o(.data) for _stream_list_lock
    fopen_locked.o(.text) refers to stdio_streams.o(.bss) for __stdin
    lc_numeric_c.o(locale$$data) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000016) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000016) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers to strcmpv7m.o(.text) for strcmp
    lc_numeric_c.o(locale$$code) refers to lc_numeric_c.o(locale$$data) for __lcnum_c_name
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    defsig_fpe_outer.o(.text) refers to defsig_fpe_inner.o(.text) for __rt_SIGFPE_inner
    defsig_fpe_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_fpe_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    defsig_rtred_outer.o(.text) refers to defsig_rtred_inner.o(.text) for __rt_SIGRTRED_inner
    defsig_rtred_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtred_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002C) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000001) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000A) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000011) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000033) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_user_alloc_1
    libinit2.o(.ARM.Collect$$libinit$$00000001) refers to fpinit.o(x$fpl$fpinit) for _fp_init
    libinit2.o(.ARM.Collect$$libinit$$00000005) refers (Weak) to init_alloc.o(.text) for _init_alloc
    libinit2.o(.ARM.Collect$$libinit$$0000000F) refers (Weak) to rt_locale_intlibspace.o(.text) for __rt_locale
    libinit2.o(.ARM.Collect$$libinit$$00000010) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers (Weak) to lc_ctype_c.o(locale$$code) for _get_lc_ctype
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers (Weak) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000024) refers (Weak) to initio.o(.text) for _initio
    libinit2.o(.ARM.Collect$$libinit$$00000026) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000027) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    libshutdown2.o(.ARM.Collect$$libshutdown$$00000005) refers (Weak) to initio.o(.text) for _terminateio
    libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F) refers (Weak) to term_alloc.o(.text) for _terminate_alloc
    flsbuf_fwide.o(.text) refers to flsbuf.o(.text) for __flsbuf
    istatus.o(x$fpl$ieeestatus) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fpclassify.o(i.__ARM_fpclassify) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    rt_heap_descriptor.o(.text) refers to rt_heap_descriptor.o(.bss) for __rt_heap_descriptor_data
    rt_heap_descriptor_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    init_alloc.o(.text) refers (Special) to hguard.o(.text) for __heap$guard
    init_alloc.o(.text) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000005) for __rt_lib_init_heap_2
    init_alloc.o(.text) refers (Special) to maybetermalloc1.o(.emb_text) for _maybe_terminate_alloc
    init_alloc.o(.text) refers to h1_extend.o(.text) for __Heap_ProvideMemory
    init_alloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    init_alloc.o(.text) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    init_alloc.o(.text) refers to h1_init.o(.text) for __Heap_Initialize
    malloc.o(.text) refers (Special) to hguard.o(.text) for __heap$guard
    malloc.o(.text) refers (Special) to init_alloc.o(.text) for _init_alloc
    malloc.o(.text) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    malloc.o(.text) refers to heapstubs.o(.text) for __Heap_Alloc
    h1_alloc.o(.text) refers (Special) to h1_init.o(.text) for __Heap_Initialize
    h1_alloc.o(.text) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    h1_alloc.o(.text) refers to init_alloc.o(.text) for __Heap_Full
    h1_alloc_mt.o(.text) refers (Special) to h1_init.o(.text) for __Heap_Initialize
    h1_alloc_mt.o(.text) refers to init_alloc.o(.text) for __Heap_Full
    h1_alloc_mt.o(.text) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    _printf_char_file_locked.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    _printf_char_file_locked.o(.text) refers to fputc.o(i._fputc$unlocked) for _fputc$unlocked
    fseek.o(.text) refers to sys_io.o(.text) for _sys_istty
    fseek.o(.text) refers to ftell.o(.text) for _ftell_internal
    fseek.o(.text) refers to stdio.o(.text) for _seterr
    stdio.o(.text) refers to sys_io.o(.text) for _sys_seek
    fwritefast.o(.text) refers to stdio.o(.text) for _writebuf
    fwritefast.o(.text) refers to flsbuf.o(.text) for __flsbuf_byte
    fwritefast.o(.text) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    fwritefast_locked.o(.text) refers to stdio.o(.text) for _writebuf
    fwritefast_locked.o(.text) refers to flsbuf.o(.text) for __flsbuf_byte
    fwritefast_locked.o(.text) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    lc_ctype_c.o(locale$$data) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000012) for __rt_lib_init_lc_ctype_2
    lc_ctype_c.o(locale$$code) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000012) for __rt_lib_init_lc_ctype_2
    lc_ctype_c.o(locale$$code) refers to strcmpv7m.o(.text) for strcmp
    lc_ctype_c.o(locale$$code) refers to lc_ctype_c.o(locale$$data) for __lcctype_c_name
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig.o(CL$$defsig) refers to defsig_abrt_inner.o(.text) for __rt_SIGABRT_inner
    defsig.o(CL$$defsig) refers to defsig_fpe_inner.o(.text) for __rt_SIGFPE_inner
    defsig.o(CL$$defsig) refers to defsig_rtred_inner.o(.text) for __rt_SIGRTRED_inner
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    assert_stdio.o(.text) refers to fputs.o(.text) for fputs
    assert_stdio.o(.text) refers to fflush.o(.text) for fflush
    assert_stdio.o(.text) refers to stdio_streams.o(.bss) for __stderr
    argv_veneer.o(.emb_text) refers to no_argv.o(.text) for __ARM_get_argv
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to sys_exit.o(.text) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    maybetermalloc2.o(.emb_text) refers (Special) to term_alloc.o(.text) for _terminate_alloc
    h1_extend.o(.text) refers to h1_free.o(.text) for free
    h1_init_mt.o(.text) refers to mutex_dummy.o(.text) for _mutex_initialize
    h1_extend_mt.o(.text) refers to h1_free_mt.o(.text) for _free_internal
    fflush.o(.text) refers to stdio.o(.text) for _fflush
    fflush.o(.text) refers to fseek.o(.text) for _fseek
    fflush.o(.text) refers to stdio_streams.o(.bss) for __stdin
    fputs.o(.text) refers to fputc.o(i.fputc) for fputc
    ftell.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    fflush_locked.o(.text) refers to stdio.o(.text) for _fflush
    fflush_locked.o(.text) refers to fseek.o(.text) for _fseek
    fflush_locked.o(.text) refers to fflush.o(.text) for _do_fflush
    fflush_locked.o(.text) refers to streamlock.o(.data) for _stream_list_lock
    fflush_locked.o(.text) refers to stdio_streams.o(.bss) for __stdin
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    heapauxa.o(.text) refers to heapauxa.o(.data) for .data
    _get_argv.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv.o(.text) refers to h1_alloc.o(.text) for malloc
    _get_argv.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv.o(.text) refers to sys_command.o(.text) for _sys_command_string
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    term_alloc.o(.text) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000010) for __rt_lib_shutdown_heap_2
    term_alloc.o(.text) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    term_alloc.o(.text) refers to h1_final.o(.text) for __Heap_Finalize
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000004) for __rt_lib_shutdown_cpp_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000002) for __rt_lib_shutdown_fini_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000009) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000011) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000012) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000006) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E) for __rt_lib_shutdown_user_alloc_1


==============================================================================

Removing Unused input sections from the image.

    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing main.o(.rrx_text), (6 bytes).
    Removing main.o(.data), (128 bytes).
    Removing gpio.o(.rev16_text), (4 bytes).
    Removing gpio.o(.revsh_text), (4 bytes).
    Removing gpio.o(.rrx_text), (6 bytes).
    Removing dma.o(.rev16_text), (4 bytes).
    Removing dma.o(.revsh_text), (4 bytes).
    Removing dma.o(.rrx_text), (6 bytes).
    Removing i2c.o(.rev16_text), (4 bytes).
    Removing i2c.o(.revsh_text), (4 bytes).
    Removing i2c.o(.rrx_text), (6 bytes).
    Removing i2c.o(i.HAL_I2C_MspDeInit), (88 bytes).
    Removing tim.o(.rev16_text), (4 bytes).
    Removing tim.o(.revsh_text), (4 bytes).
    Removing tim.o(.rrx_text), (6 bytes).
    Removing tim.o(i.HAL_TIM_Base_MspDeInit), (28 bytes).
    Removing tim.o(i.HAL_TIM_Encoder_MspDeInit), (72 bytes).
    Removing usart.o(.rev16_text), (4 bytes).
    Removing usart.o(.revsh_text), (4 bytes).
    Removing usart.o(.rrx_text), (6 bytes).
    Removing usart.o(i.HAL_UART_MspDeInit), (256 bytes).
    Removing usart.o(.bss), (64 bytes).
    Removing usart.o(.bss), (32 bytes).
    Removing stm32f4xx_it.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_it.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_it.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_msp.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_msp.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_msp.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_i2c.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_i2c.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_i2c.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_AbortCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_AddrCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_DeInit), (50 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_DisableListen_IT), (68 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_ER_IRQHandler), (186 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler), (560 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_EnableListen_IT), (58 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_ErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_GetError), (4 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_GetMode), (6 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_GetState), (6 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_IsDeviceReady), (364 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_MasterTxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Abort_IT), (98 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive), (496 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA), (348 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_IT), (196 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA), (552 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_IT), (320 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA), (452 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_IT), (212 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit), (300 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA), (348 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_IT), (184 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read), (516 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA), (460 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_IT), (220 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA), (404 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_IT), (208 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_MspInit), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive), (372 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA), (236 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_IT), (124 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA), (352 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_IT), (116 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA), (352 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_IT), (116 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit), (348 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA), (236 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_IT), (124 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_DMAAbort), (188 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_DMAError), (64 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt), (274 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_Flush_DR), (16 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_ITError), (344 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_BTF), (218 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_RXNE), (244 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_MasterRequestRead), (236 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_MasterRequestWrite), (156 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_BTF), (130 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_TXE), (182 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_Master_ADDR), (280 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_Master_SB), (140 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_MemoryTransmit_TXE_BTF), (168 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryRead), (252 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_Slave_ADDR), (70 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_Slave_AF), (144 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF), (348 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout), (112 bytes).
    Removing stm32f4xx_hal_i2c_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_i2c_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_i2c_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_rcc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_rcc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_rcc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_CSSCallback), (2 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_DeInit), (4 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_DisableCSS), (12 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_EnableCSS), (12 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_GetClockConfig), (64 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq), (12 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_GetOscConfig), (180 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_MCOConfig), (140 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler), (28 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisablePLLI2S), (52 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnablePLLI2S), (104 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKConfig), (52 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq), (96 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig), (316 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit), (308 bytes).
    Removing stm32f4xx_hal_flash.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_flash.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_flash.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_Program_Byte), (32 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_Program_DoubleWord), (44 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_Program_HalfWord), (36 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_Program_Word), (36 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_SetErrorCode), (104 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation), (80 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback), (2 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_GetError), (12 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler), (200 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_Lock), (20 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_OB_Launch), (24 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_OB_Lock), (20 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_OB_Unlock), (40 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_Program), (124 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT), (92 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_Unlock), (44 bytes).
    Removing stm32f4xx_hal_flash.o(.bss), (32 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_Erase_Sector), (80 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_FlushCaches), (84 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_MassErase), (40 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase), (152 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT), (92 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig), (64 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram), (204 bytes).
    Removing stm32f4xx_hal_flash_ramfunc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_flash_ramfunc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_flash_ramfunc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_gpio.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_gpio.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_gpio.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit), (360 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback), (2 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler), (24 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_LockPin), (34 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_ReadPin), (10 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_TogglePin), (16 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.DMA_MultiBufferSetConfig), (30 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_ChangeMemory), (14 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart), (100 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart_IT), (2432 bytes).
    Removing stm32f4xx_hal_dma.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_dma.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_dma.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_DeInit), (98 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_GetError), (4 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_GetState), (6 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_PollForTransfer), (288 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_RegisterCallback), (82 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_Start), (70 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_UnRegisterCallback), (96 bytes).
    Removing stm32f4xx_hal_pwr.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_pwr.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_pwr.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_ConfigPVD), (124 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DeInit), (24 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableBkUpAccess), (24 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisablePVD), (12 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableSEVOnPend), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableSleepOnExit), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableWakeUpPin), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableBkUpAccess), (24 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnablePVD), (12 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableSEVOnPend), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableSleepOnExit), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableWakeUpPin), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnterSLEEPMode), (36 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnterSTANDBYMode), (32 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnterSTOPMode), (60 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_PVDCallback), (2 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_PVD_IRQHandler), (28 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_ControlVoltageScaling), (88 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableBkUpReg), (60 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableFlashPowerDown), (12 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableBkUpReg), (60 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableFlashPowerDown), (12 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_GetVoltageRange), (16 bytes).
    Removing stm32f4xx_hal_cortex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_cortex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_cortex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_CORTEX_ClearEvent), (6 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_ConfigRegion), (84 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_Disable), (28 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_DisableRegion), (24 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_Enable), (36 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_EnableRegion), (24 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_ClearPendingIRQ), (26 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ), (34 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetActive), (36 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetPendingIRQ), (36 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetPriority), (82 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetPriorityGrouping), (16 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPendingIRQ), (26 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_SystemReset), (36 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_CLKSourceConfig), (24 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Callback), (2 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_DisableDBGSleepMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_DisableDBGStandbyMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_DisableDBGStopMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_EnableDBGSleepMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_EnableDBGStandbyMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_EnableDBGStopMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DeInit), (72 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DisableCompensationCell), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_EnableCompensationCell), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetDEVID), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetHalVersion), (8 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetREVID), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetTickFreq), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetTickPrio), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetUIDw0), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetUIDw1), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetUIDw2), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal.o(i.HAL_MspInit), (2 bytes).
    Removing stm32f4xx_hal.o(i.HAL_ResumeTick), (14 bytes).
    Removing stm32f4xx_hal.o(i.HAL_SetTickFreq), (36 bytes).
    Removing stm32f4xx_hal.o(i.HAL_SuspendTick), (14 bytes).
    Removing stm32f4xx_hal_exti.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_exti.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_exti.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_ClearConfigLine), (108 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_ClearPending), (20 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_GenerateSWI), (20 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_GetConfigLine), (144 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_GetHandle), (12 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_GetPending), (24 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_IRQHandler), (36 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_RegisterCallback), (14 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_SetConfigLine), (168 bytes).
    Removing stm32f4xx_hal_tim.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_tim.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_tim.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_DeInit), (92 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start), (120 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA), (192 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_IT), (128 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop), (38 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop_DMA), (58 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop_IT), (48 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigOCrefClear), (216 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigTI1Input), (16 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurstState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart), (332 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart), (332 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart), (18 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStop), (106 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart), (18 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStop), (106 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_DeInit), (76 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start), (142 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA), (428 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_IT), (182 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop), (102 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA), (172 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_IT), (144 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_ErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_GenerateEvent), (38 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_GetActiveChannel), (4 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_GetChannelState), (34 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel), (292 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_DeInit), (92 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Init), (90 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start), (228 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA), (460 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_IT), (268 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop), (82 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA), (160 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_IT), (146 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler), (304 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel), (82 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DeInit), (92 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Init), (90 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start), (200 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA), (448 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_IT), (244 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop), (124 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA), (204 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_IT), (188 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel), (230 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_DeInit), (76 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Init), (86 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start), (112 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start_IT), (132 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop), (112 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop_IT), (132 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_DeInit), (92 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start), (200 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA), (448 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_IT), (244 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop), (124 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA), (204 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_IT), (188 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_ReadCapturedValue), (42 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro), (86 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro_IT), (86 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd), (26 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt), (110 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt), (56 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt), (94 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt), (56 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMAError), (84 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt), (22 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt), (10 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMATriggerCplt), (22 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMATriggerHalfCplt), (10 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig), (140 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_TI1_SetConfig), (128 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_TI2_SetConfig), (54 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent), (112 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA), (144 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_IT), (112 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_GetChannelNState), (34 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_DeInit), (76 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init), (208 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start), (168 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA), (224 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_IT), (180 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop), (58 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA), (70 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_IT), (68 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start), (192 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA), (392 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_IT), (232 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop), (104 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA), (168 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_IT), (170 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start), (100 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT), (120 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop), (98 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT), (120 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start), (192 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA), (392 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_IT), (232 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop), (104 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA), (168 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_IT), (170 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_RemapConfig), (28 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt), (16 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt), (16 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd), (26 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt), (74 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN), (62 bytes).
    Removing stm32f4xx_hal_uart.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_uart.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_uart.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_EnableReceiver), (50 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_EnableTransmitter), (50 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_Init), (110 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_LIN_Init), (130 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_LIN_SendBreak), (60 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_EnterMuteMode), (62 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_ExitMuteMode), (62 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_Init), (144 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UARTEx_GetRxEventType), (4 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle), (240 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_IT), (78 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Abort), (210 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive), (148 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT), (152 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit), (98 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT), (104 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT), (244 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_DMAPause), (120 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_DMAResume), (114 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop), (112 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_DeInit), (54 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_GetError), (4 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_GetState), (10 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_MspInit), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Receive), (176 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Receive_DMA), (28 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT), (28 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA), (132 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_IT), (50 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMARxAbortCallback), (44 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback), (22 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATransmitCplt), (66 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATxAbortCallback), (44 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATxHalfCplt), (10 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback), (20 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_Start_Receive_IT), (54 bytes).
    Removing system_stm32f4xx.o(.rev16_text), (4 bytes).
    Removing system_stm32f4xx.o(.revsh_text), (4 bytes).
    Removing system_stm32f4xx.o(.rrx_text), (6 bytes).
    Removing system_stm32f4xx.o(i.SystemCoreClockUpdate), (124 bytes).
    Removing oled.o(.rev16_text), (4 bytes).
    Removing oled.o(.revsh_text), (4 bytes).
    Removing oled.o(.rrx_text), (6 bytes).
    Removing oled.o(i.OLED_Allfill), (52 bytes).
    Removing oled.o(i.OLED_Display_Off), (24 bytes).
    Removing oled.o(i.OLED_Display_On), (24 bytes).
    Removing oled.o(i.OLED_ShowChar), (136 bytes).
    Removing oled.o(i.OLED_ShowFloat), (266 bytes).
    Removing oled.o(i.OLED_ShowHanzi), (76 bytes).
    Removing oled.o(i.OLED_ShowHzbig), (136 bytes).
    Removing oled.o(i.OLED_ShowNum), (114 bytes).
    Removing oled.o(i.OLED_ShowPic), (64 bytes).
    Removing oled.o(i.OLED_ShowStr), (54 bytes).
    Removing oled.o(.constdata), (2712 bytes).
    Removing ringbuffer.o(i.rt_ringbuffer_getchar), (74 bytes).
    Removing ringbuffer.o(i.rt_ringbuffer_peek), (78 bytes).
    Removing ringbuffer.o(i.rt_ringbuffer_put_force), (170 bytes).
    Removing ringbuffer.o(i.rt_ringbuffer_putchar), (80 bytes).
    Removing ringbuffer.o(i.rt_ringbuffer_putchar_force), (102 bytes).
    Removing ringbuffer.o(i.rt_ringbuffer_reset), (16 bytes).
    Removing schedule.o(.rev16_text), (4 bytes).
    Removing schedule.o(.revsh_text), (4 bytes).
    Removing schedule.o(.rrx_text), (6 bytes).
    Removing oled_bsp.o(.rev16_text), (4 bytes).
    Removing oled_bsp.o(.revsh_text), (4 bytes).
    Removing oled_bsp.o(.rrx_text), (6 bytes).
    Removing oled_bsp.o(i.Oled_Printf), (48 bytes).
    Removing oled_bsp.o(i.oled_proc), (2 bytes).
    Removing key_bsp.o(.rev16_text), (4 bytes).
    Removing key_bsp.o(.revsh_text), (4 bytes).
    Removing key_bsp.o(.rrx_text), (6 bytes).
    Removing key_bsp.o(i.key_proc), (32 bytes).
    Removing key_bsp.o(i.key_read), (68 bytes).
    Removing key_bsp.o(.data), (4 bytes).
    Removing motor_bsp.o(.rev16_text), (4 bytes).
    Removing motor_bsp.o(.revsh_text), (4 bytes).
    Removing motor_bsp.o(.rrx_text), (6 bytes).
    Removing motor_bsp.o(i.Location_Speed_control), (176 bytes).
    Removing motor_bsp.o(i.motor_init), (72 bytes).
    Removing motor_bsp.o(i.motor_proc), (4 bytes).
    Removing motor_bsp.o(.bss), (64 bytes).
    Removing motor_bsp.o(.data), (12 bytes).
    Removing motor_bsp.o(.data), (1 bytes).
    Removing motor_bsp.o(.data), (16 bytes).
    Removing encoder_bsp.o(.rev16_text), (4 bytes).
    Removing encoder_bsp.o(.revsh_text), (4 bytes).
    Removing encoder_bsp.o(.rrx_text), (6 bytes).
    Removing encoder_bsp.o(i.encoder_init), (40 bytes).
    Removing encoder_bsp.o(i.encoder_proc), (24 bytes).
    Removing encoder_bsp.o(.bss), (40 bytes).
    Removing hwt101_bsp.o(.rev16_text), (4 bytes).
    Removing hwt101_bsp.o(.revsh_text), (4 bytes).
    Removing hwt101_bsp.o(.rrx_text), (6 bytes).
    Removing hwt101_bsp.o(i.hwt101_init), (40 bytes).
    Removing hwt101_bsp.o(i.hwt101_proc), (104 bytes).
    Removing hwt101_bsp.o(.bss), (68 bytes).
    Removing uart_bsp.o(.rev16_text), (4 bytes).
    Removing uart_bsp.o(.revsh_text), (4 bytes).
    Removing uart_bsp.o(.rrx_text), (6 bytes).
    Removing uart_bsp.o(i.check_motor_angle_limits), (236 bytes).
    Removing uart_bsp.o(i.process_command), (132 bytes).
    Removing uart_bsp.o(i.process_reset_command), (160 bytes).
    Removing step_motor_bsp.o(.rev16_text), (4 bytes).
    Removing step_motor_bsp.o(.revsh_text), (4 bytes).
    Removing step_motor_bsp.o(.rrx_text), (6 bytes).
    Removing step_motor_bsp.o(i.Step_Motor_Set_Pwm), (84 bytes).
    Removing step_motor_bsp.o(i.Step_Motor_Set_Speed), (200 bytes).
    Removing step_motor_bsp.o(i.step_motor_proc), (2 bytes).
    Removing pi_bsp.o(.rev16_text), (4 bytes).
    Removing pi_bsp.o(.revsh_text), (4 bytes).
    Removing pi_bsp.o(.rrx_text), (6 bytes).
    Removing gray_bsp.o(.rev16_text), (4 bytes).
    Removing gray_bsp.o(.revsh_text), (4 bytes).
    Removing gray_bsp.o(.rrx_text), (6 bytes).
    Removing gray_bsp.o(i.gray_proc), (112 bytes).
    Removing gray_bsp.o(.data), (1 bytes).
    Removing encoder_drv.o(.rev16_text), (4 bytes).
    Removing encoder_drv.o(.revsh_text), (4 bytes).
    Removing encoder_drv.o(.rrx_text), (6 bytes).
    Removing encoder_drv.o(i.Encoder_Driver_Init), (44 bytes).
    Removing encoder_drv.o(i.Encoder_Driver_Update), (96 bytes).
    Removing hwt101_driver.o(.rev16_text), (4 bytes).
    Removing hwt101_driver.o(.revsh_text), (4 bytes).
    Removing hwt101_driver.o(.rrx_text), (6 bytes).
    Removing hwt101_driver.o(i.HWT101_ConvertGyroData), (40 bytes).
    Removing hwt101_driver.o(i.HWT101_Create), (84 bytes).
    Removing hwt101_driver.o(i.HWT101_Enable), (64 bytes).
    Removing hwt101_driver.o(i.HWT101_GetData), (34 bytes).
    Removing hwt101_driver.o(i.HWT101_GetGyroZ), (40 bytes).
    Removing hwt101_driver.o(i.HWT101_GetState), (20 bytes).
    Removing hwt101_driver.o(i.HWT101_GetYaw), (40 bytes).
    Removing hwt101_driver.o(i.HWT101_ProcessBuffer), (328 bytes).
    Removing hwt101_driver.o(i.HWT101_ResetYaw), (64 bytes).
    Removing hwt101_driver.o(i.HWT101_SaveConfig), (48 bytes).
    Removing hwt101_driver.o(i.HWT101_SendCommand), (44 bytes).
    Removing hwt101_driver.o(i.HWT101_SetBaudRate), (70 bytes).
    Removing hwt101_driver.o(i.HWT101_SetOutputRate), (74 bytes).
    Removing hwt101_driver.o(i.HWT101_StartManualCalibration), (64 bytes).
    Removing hwt101_driver.o(i.HWT101_StopManualCalibration), (64 bytes).
    Removing hwt101_driver.o(i.HWT101_UnlockRegister), (36 bytes).
    Removing hwt101_driver.o(i.HWT101_ValidateParams), (16 bytes).
    Removing hardware_iic.o(.rev16_text), (4 bytes).
    Removing hardware_iic.o(.revsh_text), (4 bytes).
    Removing hardware_iic.o(.rrx_text), (6 bytes).
    Removing hardware_iic.o(i.IIC_Anolog_Normalize), (16 bytes).
    Removing hardware_iic.o(i.IIC_Get_Anolog), (22 bytes).
    Removing hardware_iic.o(i.IIC_Get_Digtal), (20 bytes).
    Removing hardware_iic.o(i.IIC_Get_Offset), (24 bytes).
    Removing hardware_iic.o(i.IIC_Get_Single_Anolog), (22 bytes).
    Removing hardware_iic.o(i.IIC_ReadByte), (32 bytes).
    Removing hardware_iic.o(i.IIC_ReadBytes), (36 bytes).
    Removing hardware_iic.o(i.IIC_WriteByte), (44 bytes).
    Removing hardware_iic.o(i.IIC_WriteBytes), (36 bytes).
    Removing hardware_iic.o(i.Ping), (30 bytes).
    Removing emm_v5.o(.rev16_text), (4 bytes).
    Removing emm_v5.o(.revsh_text), (4 bytes).
    Removing emm_v5.o(.rrx_text), (6 bytes).
    Removing emm_v5.o(i.Emm_V5_Modify_Ctrl_Mode), (56 bytes).
    Removing emm_v5.o(i.Emm_V5_Origin_Interrupt), (48 bytes).
    Removing emm_v5.o(i.Emm_V5_Origin_Modify_Params), (158 bytes).
    Removing emm_v5.o(i.Emm_V5_Origin_Set_O), (52 bytes).
    Removing emm_v5.o(i.Emm_V5_Origin_Trigger_Return), (50 bytes).
    Removing emm_v5.o(i.Emm_V5_Pos_Control), (112 bytes).
    Removing emm_v5.o(i.Emm_V5_Reset_Clog_Pro), (48 bytes).
    Removing emm_v5.o(i.Emm_V5_Synchronous_motion), (48 bytes).
    Removing mypid.o(.rev16_text), (4 bytes).
    Removing mypid.o(.revsh_text), (4 bytes).
    Removing mypid.o(.rrx_text), (6 bytes).
    Removing mypid.o(i.pid_angle_calc), (356 bytes).
    Removing mypid.o(i.pid_calc_d), (256 bytes).
    Removing mypid.o(i.pid_calc_i_separation), (420 bytes).
    Removing mypid.o(i.pid_clear), (28 bytes).
    Removing mypid.o(i.pid_yaw_calc), (320 bytes).
    Removing mypid.o(.data), (4 bytes).
    Removing mypid.o(.data), (4 bytes).
    Removing mypid.o(.data), (4 bytes).
    Removing mypid.o(.data), (4 bytes).
    Removing mypid.o(.data), (4 bytes).
    Removing mypid.o(.data), (4 bytes).
    Removing mypid.o(.data), (4 bytes).
    Removing mypid.o(.data), (4 bytes).
    Removing motor_driver_tb6612.o(.rev16_text), (4 bytes).
    Removing motor_driver_tb6612.o(.revsh_text), (4 bytes).
    Removing motor_driver_tb6612.o(.rrx_text), (6 bytes).
    Removing motor_driver_tb6612.o(i.Motor_Create), (144 bytes).
    Removing motor_driver_tb6612.o(i.Motor_GetState), (18 bytes).
    Removing motor_driver_tb6612.o(i.Motor_SetEnable), (32 bytes).
    Removing motor_driver_tb6612.o(i.Motor_SetSpeed), (288 bytes).
    Removing motor_driver_tb6612.o(i.Motor_Stop), (6 bytes).
    Removing motor_driver_tb6612.o(i.Motor_ValidateParams), (16 bytes).
    Removing motor_driver_tb6612.o(i.Speed_To_PWM), (24 bytes).
    Removing motor_driver_tb6612.o(i.TB6612_Init), (24 bytes).

619 unused section(s) (total 50978 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../Core/Src/dma.c                        0x00000000   Number         0  dma.o ABSOLUTE
    ../Core/Src/gpio.c                       0x00000000   Number         0  gpio.o ABSOLUTE
    ../Core/Src/i2c.c                        0x00000000   Number         0  i2c.o ABSOLUTE
    ../Core/Src/main.c                       0x00000000   Number         0  main.o ABSOLUTE
    ../Core/Src/stm32f4xx_hal_msp.c          0x00000000   Number         0  stm32f4xx_hal_msp.o ABSOLUTE
    ../Core/Src/stm32f4xx_it.c               0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    ../Core/Src/system_stm32f4xx.c           0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    ../Core/Src/tim.c                        0x00000000   Number         0  tim.o ABSOLUTE
    ../Core/Src/usart.c                      0x00000000   Number         0  usart.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.c 0x00000000   Number         0  stm32f4xx_hal.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.c 0x00000000   Number         0  stm32f4xx_hal_cortex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.c 0x00000000   Number         0  stm32f4xx_hal_dma.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.c 0x00000000   Number         0  stm32f4xx_hal_dma_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.c 0x00000000   Number         0  stm32f4xx_hal_exti.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.c 0x00000000   Number         0  stm32f4xx_hal_flash.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.c 0x00000000   Number         0  stm32f4xx_hal_flash_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.c 0x00000000   Number         0  stm32f4xx_hal_flash_ramfunc.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.c 0x00000000   Number         0  stm32f4xx_hal_gpio.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_i2c.c 0x00000000   Number         0  stm32f4xx_hal_i2c.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_i2c_ex.c 0x00000000   Number         0  stm32f4xx_hal_i2c_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.c 0x00000000   Number         0  stm32f4xx_hal_pwr.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.c 0x00000000   Number         0  stm32f4xx_hal_pwr_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.c 0x00000000   Number         0  stm32f4xx_hal_rcc.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.c 0x00000000   Number         0  stm32f4xx_hal_rcc_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.c 0x00000000   Number         0  stm32f4xx_hal_tim.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.c 0x00000000   Number         0  stm32f4xx_hal_tim_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_uart.c 0x00000000   Number         0  stm32f4xx_hal_uart.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_copy.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_div0.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_heap_descriptor.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_heap_descriptor_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_ctype_table.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  aeabi_ldiv0_sigfpe.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  aeabi_ldiv0.o ABSOLUTE
    ../clib/angel/scatter.s                  0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  mutex_dummy.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  indicate_semi.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_io.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_exit.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/assert.c                         0x00000000   Number         0  assert_stdio.o ABSOLUTE
    ../clib/bigflt.c                         0x00000000   Number         0  bigflt0.o ABSOLUTE
    ../clib/btod.s                           0x00000000   Number         0  btod.o ABSOLUTE
    ../clib/ctype.c                          0x00000000   Number         0  isspace.o ABSOLUTE
    ../clib/fenv.c                           0x00000000   Number         0  _rserrno.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_extend_mt.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_extend.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_init.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_final.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_init_mt.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_free_mt.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_final_mt.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_alloc.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_free.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_alloc_mt.o ABSOLUTE
    ../clib/heap2.c                          0x00000000   Number         0  heap2mt.o ABSOLUTE
    ../clib/heap2.c                          0x00000000   Number         0  fdtree.o ABSOLUTE
    ../clib/heap2.c                          0x00000000   Number         0  heap2.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  maybetermalloc1.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  maybetermalloc2.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  maybetermalloc2.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  free.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hguard.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  maybetermalloc1.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  init_alloc.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  malloc.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  heapstubs.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  term_alloc.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxa.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/locale.c                         0x00000000   Number         0  _wcrtomb.o ABSOLUTE
    ../clib/locale.s                         0x00000000   Number         0  lc_ctype_c.o ABSOLUTE
    ../clib/locale.s                         0x00000000   Number         0  lc_numeric_c.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  lludiv10.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  lludivv7m.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memclr_w.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memcpy_v6.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  strcmpv7m.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memcpy_w.o ABSOLUTE
    ../clib/misc.s                           0x00000000   Number         0  printf_stubs.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_file.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _snputc.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _sputc.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_hex.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_nopercent.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_common.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_wchar.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ll_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_file_locked.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_truncate.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_str.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_infnan.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  noretval__2printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ll_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  vsnprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_int_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __2printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_pad.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_int.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_longlong_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_charcount.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_wctomb.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_intcommon.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss_wp.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_llo.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_ls.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_lc.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_l.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_ll.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_llu.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_lld.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_lli.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent_end.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_a.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_g.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_u.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_d.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_i.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_f.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_p.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_x.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_n.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_s.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_e.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_c.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_llx.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_o.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  scanf_char.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _scanf_int.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  __0sscanf.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _scanf.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _sgetc.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _chval.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_outer.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  fseek.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  fwritefast.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  fopen_locked.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  stdio_streams.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  setvbuf_locked.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  fclose.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  fopen.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  streamlock.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  setvbuf.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  flsbuf.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  initio_locked.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  ferror_locked.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  fputc_locked.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  initio.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  fputc.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  ferror.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  fputs.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  fflush_locked.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  fputs_locked.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  ftell.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  fflush.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  fwritefast_locked.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  stdio.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  abort.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  strlen.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  strncmp.o ABSOLUTE
    ../clib/wchar.c                          0x00000000   Number         0  flsbuf_fwide.o ABSOLUTE
    ../fplib/d2f.s                           0x00000000   Number         0  d2f.o ABSOLUTE
    ../fplib/daddsub.s                       0x00000000   Number         0  daddsub_clz.o ABSOLUTE
    ../fplib/ddiv.s                          0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/dmul.s                          0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/dnaninf.s                       0x00000000   Number         0  dnaninf.o ABSOLUTE
    ../fplib/drem.s                          0x00000000   Number         0  drem_clz.o ABSOLUTE
    ../fplib/dretinf.s                       0x00000000   Number         0  dretinf.o ABSOLUTE
    ../fplib/f2d.s                           0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/fnaninf.s                       0x00000000   Number         0  fnaninf.o ABSOLUTE
    ../fplib/fpinit.s                        0x00000000   Number         0  fpinit.o ABSOLUTE
    ../fplib/fretinf.s                       0x00000000   Number         0  fretinf.o ABSOLUTE
    ../fplib/istatus.s                       0x00000000   Number         0  istatus.o ABSOLUTE
    ../fplib/printf1.s                       0x00000000   Number         0  printf1.o ABSOLUTE
    ../fplib/printf2.s                       0x00000000   Number         0  printf2.o ABSOLUTE
    ../fplib/printf2a.s                      0x00000000   Number         0  printf2a.o ABSOLUTE
    ../fplib/printf2b.s                      0x00000000   Number         0  printf2b.o ABSOLUTE
    ../fplib/usenofp.s                       0x00000000   Number         0  usenofp.o ABSOLUTE
    ../mathlib/dunder.c                      0x00000000   Number         0  dunder.o ABSOLUTE
    ../mathlib/fmod.c                        0x00000000   Number         0  fmod_x.o ABSOLUTE
    ../mathlib/fmod.c                        0x00000000   Number         0  fmod.o ABSOLUTE
    ../mathlib/fpclassify.c                  0x00000000   Number         0  fpclassify.o ABSOLUTE
    ..\Core\Src\dma.c                        0x00000000   Number         0  dma.o ABSOLUTE
    ..\Core\Src\gpio.c                       0x00000000   Number         0  gpio.o ABSOLUTE
    ..\Core\Src\i2c.c                        0x00000000   Number         0  i2c.o ABSOLUTE
    ..\Core\Src\main.c                       0x00000000   Number         0  main.o ABSOLUTE
    ..\Core\Src\stm32f4xx_hal_msp.c          0x00000000   Number         0  stm32f4xx_hal_msp.o ABSOLUTE
    ..\Core\Src\stm32f4xx_it.c               0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    ..\Core\Src\system_stm32f4xx.c           0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    ..\Core\Src\tim.c                        0x00000000   Number         0  tim.o ABSOLUTE
    ..\Core\Src\usart.c                      0x00000000   Number         0  usart.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal.c 0x00000000   Number         0  stm32f4xx_hal.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_cortex.c 0x00000000   Number         0  stm32f4xx_hal_cortex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_dma.c 0x00000000   Number         0  stm32f4xx_hal_dma.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_dma_ex.c 0x00000000   Number         0  stm32f4xx_hal_dma_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_exti.c 0x00000000   Number         0  stm32f4xx_hal_exti.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_flash.c 0x00000000   Number         0  stm32f4xx_hal_flash.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_flash_ex.c 0x00000000   Number         0  stm32f4xx_hal_flash_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_flash_ramfunc.c 0x00000000   Number         0  stm32f4xx_hal_flash_ramfunc.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_gpio.c 0x00000000   Number         0  stm32f4xx_hal_gpio.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_i2c.c 0x00000000   Number         0  stm32f4xx_hal_i2c.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_i2c_ex.c 0x00000000   Number         0  stm32f4xx_hal_i2c_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_pwr.c 0x00000000   Number         0  stm32f4xx_hal_pwr.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_pwr_ex.c 0x00000000   Number         0  stm32f4xx_hal_pwr_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_rcc.c 0x00000000   Number         0  stm32f4xx_hal_rcc.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_rcc_ex.c 0x00000000   Number         0  stm32f4xx_hal_rcc_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_tim.c 0x00000000   Number         0  stm32f4xx_hal_tim.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_tim_ex.c 0x00000000   Number         0  stm32f4xx_hal_tim_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_uart.c 0x00000000   Number         0  stm32f4xx_hal_uart.o ABSOLUTE
    ..\OLED\oled.c                           0x00000000   Number         0  oled.o ABSOLUTE
    ..\TB6612\motor_driver_tb6612.c          0x00000000   Number         0  motor_driver_tb6612.o ABSOLUTE
    ..\\OLED\\oled.c                         0x00000000   Number         0  oled.o ABSOLUTE
    ..\\TB6612\\motor_driver_tb6612.c        0x00000000   Number         0  motor_driver_tb6612.o ABSOLUTE
    ..\\app\\Emm_V5.c                        0x00000000   Number         0  emm_v5.o ABSOLUTE
    ..\\app\\encoder_drv.c                   0x00000000   Number         0  encoder_drv.o ABSOLUTE
    ..\\app\\hardware_iic.c                  0x00000000   Number         0  hardware_iic.o ABSOLUTE
    ..\\app\\hwt101_driver.c                 0x00000000   Number         0  hwt101_driver.o ABSOLUTE
    ..\\app\\mypid.c                         0x00000000   Number         0  mypid.o ABSOLUTE
    ..\\bsp\\encoder_bsp.c                   0x00000000   Number         0  encoder_bsp.o ABSOLUTE
    ..\\bsp\\gray_bsp.c                      0x00000000   Number         0  gray_bsp.o ABSOLUTE
    ..\\bsp\\hwt101_bsp.c                    0x00000000   Number         0  hwt101_bsp.o ABSOLUTE
    ..\\bsp\\key_bsp.c                       0x00000000   Number         0  key_bsp.o ABSOLUTE
    ..\\bsp\\motor_bsp.c                     0x00000000   Number         0  motor_bsp.o ABSOLUTE
    ..\\bsp\\oled_bsp.c                      0x00000000   Number         0  oled_bsp.o ABSOLUTE
    ..\\bsp\\pi_bsp.c                        0x00000000   Number         0  pi_bsp.o ABSOLUTE
    ..\\bsp\\schedule.c                      0x00000000   Number         0  schedule.o ABSOLUTE
    ..\\bsp\\step_motor_bsp.c                0x00000000   Number         0  step_motor_bsp.o ABSOLUTE
    ..\\bsp\\uart_bsp.c                      0x00000000   Number         0  uart_bsp.o ABSOLUTE
    ..\app\Emm_V5.c                          0x00000000   Number         0  emm_v5.o ABSOLUTE
    ..\app\encoder_drv.c                     0x00000000   Number         0  encoder_drv.o ABSOLUTE
    ..\app\hardware_iic.c                    0x00000000   Number         0  hardware_iic.o ABSOLUTE
    ..\app\hwt101_driver.c                   0x00000000   Number         0  hwt101_driver.o ABSOLUTE
    ..\app\motor_driver.c                    0x00000000   Number         0  motor_driver.o ABSOLUTE
    ..\app\mypid.c                           0x00000000   Number         0  mypid.o ABSOLUTE
    ..\bsp\encoder_bsp.c                     0x00000000   Number         0  encoder_bsp.o ABSOLUTE
    ..\bsp\gray_bsp.c                        0x00000000   Number         0  gray_bsp.o ABSOLUTE
    ..\bsp\hwt101_bsp.c                      0x00000000   Number         0  hwt101_bsp.o ABSOLUTE
    ..\bsp\key_bsp.c                         0x00000000   Number         0  key_bsp.o ABSOLUTE
    ..\bsp\motor_bsp.c                       0x00000000   Number         0  motor_bsp.o ABSOLUTE
    ..\bsp\oled_bsp.c                        0x00000000   Number         0  oled_bsp.o ABSOLUTE
    ..\bsp\pi_bsp.c                          0x00000000   Number         0  pi_bsp.o ABSOLUTE
    ..\bsp\schedule.c                        0x00000000   Number         0  schedule.o ABSOLUTE
    ..\bsp\step_motor_bsp.c                  0x00000000   Number         0  step_motor_bsp.o ABSOLUTE
    ..\bsp\uart_bsp.c                        0x00000000   Number         0  uart_bsp.o ABSOLUTE
    ..\ringbuffer\ringbuffer.c               0x00000000   Number         0  ringbuffer.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    startup_stm32f407xx.s                    0x00000000   Number         0  startup_stm32f407xx.o ABSOLUTE
    RESET                                    0x08000000   Section      392  startup_stm32f407xx.o(RESET)
    !!!main                                  0x08000188   Section        8  __main.o(!!!main)
    !!!scatter                               0x08000190   Section       52  __scatter.o(!!!scatter)
    !!handler_copy                           0x080001c4   Section       26  __scatter_copy.o(!!handler_copy)
    !!handler_zi                             0x080001e0   Section       28  __scatter_zi.o(!!handler_zi)
    .ARM.Collect$$_printf_percent$$00000000  0x080001fc   Section        0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    .ARM.Collect$$_printf_percent$$00000001  0x080001fc   Section        6  _printf_n.o(.ARM.Collect$$_printf_percent$$00000001)
    .ARM.Collect$$_printf_percent$$00000002  0x08000202   Section        6  _printf_p.o(.ARM.Collect$$_printf_percent$$00000002)
    .ARM.Collect$$_printf_percent$$00000003  0x08000208   Section        6  _printf_f.o(.ARM.Collect$$_printf_percent$$00000003)
    .ARM.Collect$$_printf_percent$$00000004  0x0800020e   Section        6  _printf_e.o(.ARM.Collect$$_printf_percent$$00000004)
    .ARM.Collect$$_printf_percent$$00000005  0x08000214   Section        6  _printf_g.o(.ARM.Collect$$_printf_percent$$00000005)
    .ARM.Collect$$_printf_percent$$00000006  0x0800021a   Section        6  _printf_a.o(.ARM.Collect$$_printf_percent$$00000006)
    .ARM.Collect$$_printf_percent$$00000007  0x08000220   Section       10  _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007)
    .ARM.Collect$$_printf_percent$$00000008  0x0800022a   Section        6  _printf_i.o(.ARM.Collect$$_printf_percent$$00000008)
    .ARM.Collect$$_printf_percent$$00000009  0x08000230   Section        6  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    .ARM.Collect$$_printf_percent$$0000000A  0x08000236   Section        6  _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A)
    .ARM.Collect$$_printf_percent$$0000000B  0x0800023c   Section        6  _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B)
    .ARM.Collect$$_printf_percent$$0000000C  0x08000242   Section        6  _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C)
    .ARM.Collect$$_printf_percent$$0000000D  0x08000248   Section        6  _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D)
    .ARM.Collect$$_printf_percent$$0000000E  0x0800024e   Section        6  _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E)
    .ARM.Collect$$_printf_percent$$0000000F  0x08000254   Section        6  _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F)
    .ARM.Collect$$_printf_percent$$00000010  0x0800025a   Section        6  _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010)
    .ARM.Collect$$_printf_percent$$00000011  0x08000260   Section        6  _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011)
    .ARM.Collect$$_printf_percent$$00000012  0x08000266   Section       10  _printf_l.o(.ARM.Collect$$_printf_percent$$00000012)
    .ARM.Collect$$_printf_percent$$00000013  0x08000270   Section        6  _printf_c.o(.ARM.Collect$$_printf_percent$$00000013)
    .ARM.Collect$$_printf_percent$$00000014  0x08000276   Section        6  _printf_s.o(.ARM.Collect$$_printf_percent$$00000014)
    .ARM.Collect$$_printf_percent$$00000015  0x0800027c   Section        6  _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015)
    .ARM.Collect$$_printf_percent$$00000016  0x08000282   Section        6  _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016)
    .ARM.Collect$$_printf_percent$$00000017  0x08000288   Section        4  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    .ARM.Collect$$libinit$$00000000          0x0800028c   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000001          0x0800028e   Section        4  libinit2.o(.ARM.Collect$$libinit$$00000001)
    .ARM.Collect$$libinit$$00000004          0x08000292   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    .ARM.Collect$$libinit$$0000000A          0x08000292   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    .ARM.Collect$$libinit$$0000000C          0x08000292   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x08000292   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$0000000F          0x08000292   Section        6  libinit2.o(.ARM.Collect$$libinit$$0000000F)
    .ARM.Collect$$libinit$$00000011          0x08000298   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    .ARM.Collect$$libinit$$00000012          0x08000298   Section       12  libinit2.o(.ARM.Collect$$libinit$$00000012)
    .ARM.Collect$$libinit$$00000013          0x080002a4   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000015          0x080002a4   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000016          0x080002a4   Section       10  libinit2.o(.ARM.Collect$$libinit$$00000016)
    .ARM.Collect$$libinit$$00000017          0x080002ae   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000019          0x080002ae   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x080002ae   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x080002ae   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x080002ae   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x080002ae   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x080002ae   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000025          0x080002ae   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$0000002C          0x080002ae   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    .ARM.Collect$$libinit$$0000002E          0x080002ae   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x080002ae   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000032          0x080002ae   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000033          0x080002ae   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000033)
    .ARM.Collect$$libshutdown$$00000000      0x080002b0   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000002      0x080002b2   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    .ARM.Collect$$libshutdown$$00000004      0x080002b2   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    .ARM.Collect$$libshutdown$$00000006      0x080002b2   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    .ARM.Collect$$libshutdown$$00000009      0x080002b2   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    .ARM.Collect$$libshutdown$$0000000C      0x080002b2   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    .ARM.Collect$$libshutdown$$0000000E      0x080002b2   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    .ARM.Collect$$libshutdown$$00000011      0x080002b2   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000011)
    .ARM.Collect$$libshutdown$$00000012      0x080002b2   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$00000012)
    .ARM.Collect$$rtentry$$00000000          0x080002b4   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x080002b4   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$00000004          0x080002b4   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    .ARM.Collect$$rtentry$$00000009          0x080002ba   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    .ARM.Collect$$rtentry$$0000000A          0x080002ba   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x080002be   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x080002be   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x080002c6   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x080002c8   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$00000003           0x080002c8   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    .ARM.Collect$$rtexit$$00000004           0x080002cc   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    .text                                    0x080002d4   Section       64  startup_stm32f407xx.o(.text)
    $v0                                      0x080002d4   Number         0  startup_stm32f407xx.o(.text)
    .text                                    0x08000314   Section      238  lludivv7m.o(.text)
    .text                                    0x08000404   Section        0  vsnprintf.o(.text)
    .text                                    0x08000438   Section        0  _printf_str.o(.text)
    .text                                    0x0800048c   Section        0  __printf_flags_ss_wp.o(.text)
    .text                                    0x08000614   Section        0  __0sscanf.o(.text)
    .text                                    0x08000650   Section        0  _scanf_int.o(.text)
    .text                                    0x0800079c   Section        0  abort.o(.text)
    .text                                    0x080007b2   Section        0  strncmp.o(.text)
    .text                                    0x08000848   Section      138  rt_memcpy_v6.o(.text)
    .text                                    0x080008d2   Section       78  rt_memclr_w.o(.text)
    .text                                    0x08000920   Section        0  heapauxi.o(.text)
    .text                                    0x08000928   Section        0  sys_exit.o(.text)
    .text                                    0x08000934   Section        0  _printf_pad.o(.text)
    .text                                    0x08000982   Section        0  _printf_truncate.o(.text)
    .text                                    0x080009a8   Section        0  _printf_dec.o(.text)
    .text                                    0x08000a20   Section        0  _printf_charcount.o(.text)
    .text                                    0x08000a48   Section        0  _printf_char_common.o(.text)
    _printf_input_char                       0x08000a49   Thumb Code    10  _printf_char_common.o(.text)
    .text                                    0x08000a78   Section        0  _sputc.o(.text)
    .text                                    0x08000a82   Section        0  _snputc.o(.text)
    .text                                    0x08000a92   Section        0  _printf_char.o(.text)
    .text                                    0x08000ac0   Section        0  _printf_wctomb.o(.text)
    .text                                    0x08000b7c   Section        0  _printf_longlong_dec.o(.text)
    .text                                    0x08000bf8   Section        0  _printf_oct_int_ll.o(.text)
    _printf_longlong_oct_internal            0x08000bf9   Thumb Code     0  _printf_oct_int_ll.o(.text)
    .text                                    0x08000c68   Section        0  _printf_hex_int_ll_ptr.o(.text)
    _printf_hex_common                       0x08000c69   Thumb Code     0  _printf_hex_int_ll_ptr.o(.text)
    .text                                    0x08000cfc   Section        0  _chval.o(.text)
    .text                                    0x08000d18   Section        0  scanf_char.o(.text)
    _scanf_char_input                        0x08000d19   Thumb Code    12  scanf_char.o(.text)
    .text                                    0x08000d44   Section        0  _sgetc.o(.text)
    .text                                    0x08000d84   Section        0  defsig_abrt_outer.o(.text)
    .text                                    0x08000d92   Section      100  rt_memcpy_w.o(.text)
    .text                                    0x08000df6   Section        2  use_no_semi.o(.text)
    .text                                    0x08000df8   Section      138  lludiv10.o(.text)
    .text                                    0x08000df8   Section        0  indicate_semi.o(.text)
    .text                                    0x08000e82   Section        0  isspace.o(.text)
    .text                                    0x08000e94   Section        0  _printf_intcommon.o(.text)
    .text                                    0x08000f46   Section        0  _printf_fp_dec.o(.text)
    _fp_digits                               0x08000f49   Thumb Code   432  _printf_fp_dec.o(.text)
    .text                                    0x08001364   Section        0  _printf_fp_hex.o(.text)
    .text                                    0x08001660   Section        0  _printf_wchar.o(.text)
    .text                                    0x0800168c   Section        0  _scanf.o(.text)
    .text                                    0x08001a00   Section        0  _wcrtomb.o(.text)
    .text                                    0x08001a40   Section        0  defsig_exit.o(.text)
    .text                                    0x08001a4c   Section        0  defsig_abrt_inner.o(.text)
    .text                                    0x08001a7c   Section        8  libspace.o(.text)
    .text                                    0x08001a84   Section       74  sys_stackheap_outer.o(.text)
    .text                                    0x08001ad0   Section       16  rt_ctype_table.o(.text)
    .text                                    0x08001ae0   Section        8  rt_locale_intlibspace.o(.text)
    .text                                    0x08001ae8   Section        0  _printf_fp_infnan.o(.text)
    .text                                    0x08001b68   Section        0  bigflt0.o(.text)
    .text                                    0x08001c4c   Section        0  exit.o(.text)
    .text                                    0x08001c5e   Section        0  defsig_general.o(.text)
    .text                                    0x08001c90   Section        0  sys_wrch.o(.text)
    .text                                    0x08001ca0   Section      128  strcmpv7m.o(.text)
    CL$$btod_d2e                             0x08001d20   Section       62  btod.o(CL$$btod_d2e)
    CL$$btod_d2e_denorm_low                  0x08001d5e   Section       70  btod.o(CL$$btod_d2e_denorm_low)
    CL$$btod_d2e_norm_op1                    0x08001da4   Section       96  btod.o(CL$$btod_d2e_norm_op1)
    CL$$btod_div_common                      0x08001e04   Section      824  btod.o(CL$$btod_div_common)
    CL$$btod_e2e                             0x0800213c   Section      220  btod.o(CL$$btod_e2e)
    CL$$btod_ediv                            0x08002218   Section       42  btod.o(CL$$btod_ediv)
    CL$$btod_emul                            0x08002242   Section       42  btod.o(CL$$btod_emul)
    CL$$btod_mult_common                     0x0800226c   Section      580  btod.o(CL$$btod_mult_common)
    i.BusFault_Handler                       0x080024b0   Section        0  stm32f4xx_it.o(i.BusFault_Handler)
    i.DMA1_Stream2_IRQHandler                0x080024b4   Section        0  stm32f4xx_it.o(i.DMA1_Stream2_IRQHandler)
    i.DMA1_Stream5_IRQHandler                0x080024c0   Section        0  stm32f4xx_it.o(i.DMA1_Stream5_IRQHandler)
    i.DMA2_Stream1_IRQHandler                0x080024cc   Section        0  stm32f4xx_it.o(i.DMA2_Stream1_IRQHandler)
    i.DMA_CalcBaseAndBitshift                0x080024d8   Section        0  stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift)
    DMA_CalcBaseAndBitshift                  0x080024d9   Thumb Code    34  stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift)
    i.DMA_CheckFifoParam                     0x08002500   Section        0  stm32f4xx_hal_dma.o(i.DMA_CheckFifoParam)
    DMA_CheckFifoParam                       0x08002501   Thumb Code    84  stm32f4xx_hal_dma.o(i.DMA_CheckFifoParam)
    i.DMA_SetConfig                          0x08002554   Section        0  stm32f4xx_hal_dma.o(i.DMA_SetConfig)
    DMA_SetConfig                            0x08002555   Thumb Code    40  stm32f4xx_hal_dma.o(i.DMA_SetConfig)
    i.DebugMon_Handler                       0x0800257c   Section        0  stm32f4xx_it.o(i.DebugMon_Handler)
    i.Emm_V5_En_Control                      0x0800257e   Section        0  emm_v5.o(i.Emm_V5_En_Control)
    i.Emm_V5_Parse_Response                  0x080025b6   Section        0  emm_v5.o(i.Emm_V5_Parse_Response)
    i.Emm_V5_Read_Sys_Params                 0x080027a2   Section        0  emm_v5.o(i.Emm_V5_Read_Sys_Params)
    i.Emm_V5_Reset_CurPos_To_Zero            0x08002830   Section        0  emm_v5.o(i.Emm_V5_Reset_CurPos_To_Zero)
    i.Emm_V5_Stop_Now                        0x08002860   Section        0  emm_v5.o(i.Emm_V5_Stop_Now)
    i.Emm_V5_Vel_Control                     0x08002894   Section        0  emm_v5.o(i.Emm_V5_Vel_Control)
    i.Error_Handler                          0x0800295c   Section        0  main.o(i.Error_Handler)
    i.HAL_DMA_Abort                          0x08002960   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_Abort)
    i.HAL_DMA_Abort_IT                       0x080029f2   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT)
    i.HAL_DMA_IRQHandler                     0x08002a18   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler)
    i.HAL_DMA_Init                           0x08002bb8   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_Init)
    i.HAL_DMA_Start_IT                       0x08002c8c   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT)
    i.HAL_Delay                              0x08002cfc   Section        0  stm32f4xx_hal.o(i.HAL_Delay)
    i.HAL_GPIO_Init                          0x08002d20   Section        0  stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init)
    i.HAL_GPIO_WritePin                      0x08002f10   Section        0  stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin)
    i.HAL_GetTick                            0x08002f1c   Section        0  stm32f4xx_hal.o(i.HAL_GetTick)
    i.HAL_I2C_Init                           0x08002f28   Section        0  stm32f4xx_hal_i2c.o(i.HAL_I2C_Init)
    i.HAL_I2C_Mem_Write                      0x080030b0   Section        0  stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write)
    i.HAL_I2C_MspInit                        0x080031e0   Section        0  i2c.o(i.HAL_I2C_MspInit)
    i.HAL_IncTick                            0x0800328c   Section        0  stm32f4xx_hal.o(i.HAL_IncTick)
    i.HAL_Init                               0x0800329c   Section        0  stm32f4xx_hal.o(i.HAL_Init)
    i.HAL_InitTick                           0x080032d0   Section        0  stm32f4xx_hal.o(i.HAL_InitTick)
    i.HAL_MspInit                            0x08003310   Section        0  stm32f4xx_hal_msp.o(i.HAL_MspInit)
    i.HAL_NVIC_EnableIRQ                     0x08003340   Section        0  stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ)
    i.HAL_NVIC_SetPriority                   0x0800335c   Section        0  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    i.HAL_NVIC_SetPriorityGrouping           0x0800339c   Section        0  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    i.HAL_RCC_ClockConfig                    0x080033c0   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    i.HAL_RCC_GetPCLK1Freq                   0x080034f4   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq)
    i.HAL_RCC_GetPCLK2Freq                   0x08003514   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq)
    i.HAL_RCC_GetSysClockFreq                0x08003534   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    i.HAL_RCC_OscConfig                      0x08003598   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    i.HAL_SYSTICK_Config                     0x08003904   Section        0  stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config)
    i.HAL_TIMEx_ConfigBreakDeadTime          0x0800392c   Section        0  stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigBreakDeadTime)
    i.HAL_TIMEx_MasterConfigSynchronization  0x08003980   Section        0  stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization)
    i.HAL_TIM_Base_Init                      0x08003a10   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init)
    i.HAL_TIM_Base_MspInit                   0x08003a6c   Section        0  tim.o(i.HAL_TIM_Base_MspInit)
    i.HAL_TIM_ConfigClockSource              0x08003a94   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource)
    i.HAL_TIM_Encoder_Init                   0x08003b70   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init)
    i.HAL_TIM_Encoder_MspInit                0x08003c14   Section        0  tim.o(i.HAL_TIM_Encoder_MspInit)
    i.HAL_TIM_MspPostInit                    0x08003cb8   Section        0  tim.o(i.HAL_TIM_MspPostInit)
    i.HAL_TIM_PWM_ConfigChannel              0x08003d0c   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel)
    i.HAL_TIM_PWM_Init                       0x08003dd8   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init)
    i.HAL_TIM_PWM_MspInit                    0x08003e32   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_MspInit)
    i.HAL_UARTEx_ReceiveToIdle_DMA           0x08003e34   Section        0  stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA)
    i.HAL_UARTEx_RxEventCallback             0x08003e80   Section        0  usart.o(i.HAL_UARTEx_RxEventCallback)
    i.HAL_UART_ErrorCallback                 0x08003f00   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback)
    i.HAL_UART_IRQHandler                    0x08003f04   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler)
    i.HAL_UART_Init                          0x08004184   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_Init)
    i.HAL_UART_MspInit                       0x080041e8   Section        0  usart.o(i.HAL_UART_MspInit)
    i.HAL_UART_RxCpltCallback                0x080044c0   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_RxCpltCallback)
    i.HAL_UART_RxHalfCpltCallback            0x080044c2   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback)
    i.HAL_UART_Transmit                      0x080044c4   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_Transmit)
    i.HAL_UART_TxCpltCallback                0x08004564   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback)
    i.HardFault_Handler                      0x08004566   Section        0  stm32f4xx_it.o(i.HardFault_Handler)
    i.I2C_IsAcknowledgeFailed                0x08004568   Section        0  stm32f4xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed)
    I2C_IsAcknowledgeFailed                  0x08004569   Thumb Code    46  stm32f4xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed)
    i.I2C_RequestMemoryWrite                 0x08004598   Section        0  stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryWrite)
    I2C_RequestMemoryWrite                   0x08004599   Thumb Code   162  stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryWrite)
    i.I2C_WaitOnBTFFlagUntilTimeout          0x08004640   Section        0  stm32f4xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout)
    I2C_WaitOnBTFFlagUntilTimeout            0x08004641   Thumb Code    86  stm32f4xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout)
    i.I2C_WaitOnFlagUntilTimeout             0x08004698   Section        0  stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout)
    I2C_WaitOnFlagUntilTimeout               0x08004699   Thumb Code   144  stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout)
    i.I2C_WaitOnMasterAddressFlagUntilTimeout 0x08004728   Section        0  stm32f4xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout)
    I2C_WaitOnMasterAddressFlagUntilTimeout  0x08004729   Thumb Code   188  stm32f4xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout)
    i.I2C_WaitOnTXEFlagUntilTimeout          0x080047e4   Section        0  stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout)
    I2C_WaitOnTXEFlagUntilTimeout            0x080047e5   Thumb Code    86  stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout)
    i.MX_DMA_Init                            0x0800483c   Section        0  dma.o(i.MX_DMA_Init)
    i.MX_GPIO_Init                           0x08004898   Section        0  gpio.o(i.MX_GPIO_Init)
    i.MX_I2C1_Init                           0x080049b8   Section        0  i2c.o(i.MX_I2C1_Init)
    i.MX_I2C2_Init                           0x080049f8   Section        0  i2c.o(i.MX_I2C2_Init)
    i.MX_TIM1_Init                           0x08004a38   Section        0  tim.o(i.MX_TIM1_Init)
    i.MX_TIM3_Init                           0x08004b10   Section        0  tim.o(i.MX_TIM3_Init)
    i.MX_TIM4_Init                           0x08004b7c   Section        0  tim.o(i.MX_TIM4_Init)
    i.MX_UART4_Init                          0x08004be8   Section        0  usart.o(i.MX_UART4_Init)
    i.MX_UART5_Init                          0x08004c38   Section        0  usart.o(i.MX_UART5_Init)
    i.MX_USART1_UART_Init                    0x08004c70   Section        0  usart.o(i.MX_USART1_UART_Init)
    i.MX_USART2_UART_Init                    0x08004ca8   Section        0  usart.o(i.MX_USART2_UART_Init)
    i.MX_USART3_UART_Init                    0x08004cf8   Section        0  usart.o(i.MX_USART3_UART_Init)
    i.MX_USART6_UART_Init                    0x08004d30   Section        0  usart.o(i.MX_USART6_UART_Init)
    i.MemManage_Handler                      0x08004d80   Section        0  stm32f4xx_it.o(i.MemManage_Handler)
    i.NMI_Handler                            0x08004d82   Section        0  stm32f4xx_it.o(i.NMI_Handler)
    i.OLED_Clear                             0x08004d84   Section        0  oled.o(i.OLED_Clear)
    i.OLED_Init                              0x08004db8   Section        0  oled.o(i.OLED_Init)
    i.OLED_Set_Position                      0x08004de8   Section        0  oled.o(i.OLED_Set_Position)
    i.OLED_Write_cmd                         0x08004e0c   Section        0  oled.o(i.OLED_Write_cmd)
    i.OLED_Write_data                        0x08004e30   Section        0  oled.o(i.OLED_Write_data)
    i.PID_INIT                               0x08004e54   Section        0  mypid.o(i.PID_INIT)
    i.PID_struct_init                        0x08004f24   Section        0  mypid.o(i.PID_struct_init)
    i.PendSV_Handler                         0x08004f68   Section        0  stm32f4xx_it.o(i.PendSV_Handler)
    i.SVC_Handler                            0x08004f6a   Section        0  stm32f4xx_it.o(i.SVC_Handler)
    i.Step_Motor_Init                        0x08004f6c   Section        0  step_motor_bsp.o(i.Step_Motor_Init)
    i.Step_Motor_Set_Speed_my                0x08004f98   Section        0  step_motor_bsp.o(i.Step_Motor_Set_Speed_my)
    i.Step_Motor_Stop                        0x0800505c   Section        0  step_motor_bsp.o(i.Step_Motor_Stop)
    i.SysTick_Handler                        0x08005080   Section        0  stm32f4xx_it.o(i.SysTick_Handler)
    i.SystemClock_Config                     0x08005084   Section        0  main.o(i.SystemClock_Config)
    i.SystemInit                             0x08005114   Section        0  system_stm32f4xx.o(i.SystemInit)
    i.TIM_Base_SetConfig                     0x08005124   Section        0  stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig)
    i.TIM_ETR_SetConfig                      0x080051f4   Section        0  stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig)
    i.TIM_ITRx_SetConfig                     0x08005208   Section        0  stm32f4xx_hal_tim.o(i.TIM_ITRx_SetConfig)
    TIM_ITRx_SetConfig                       0x08005209   Thumb Code    16  stm32f4xx_hal_tim.o(i.TIM_ITRx_SetConfig)
    i.TIM_OC1_SetConfig                      0x08005218   Section        0  stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig)
    TIM_OC1_SetConfig                        0x08005219   Thumb Code    88  stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig)
    i.TIM_OC2_SetConfig                      0x08005278   Section        0  stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig)
    i.TIM_OC3_SetConfig                      0x080052e4   Section        0  stm32f4xx_hal_tim.o(i.TIM_OC3_SetConfig)
    TIM_OC3_SetConfig                        0x080052e5   Thumb Code    96  stm32f4xx_hal_tim.o(i.TIM_OC3_SetConfig)
    i.TIM_OC4_SetConfig                      0x0800534c   Section        0  stm32f4xx_hal_tim.o(i.TIM_OC4_SetConfig)
    TIM_OC4_SetConfig                        0x0800534d   Thumb Code    70  stm32f4xx_hal_tim.o(i.TIM_OC4_SetConfig)
    i.TIM_TI1_ConfigInputStage               0x0800539c   Section        0  stm32f4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage)
    TIM_TI1_ConfigInputStage                 0x0800539d   Thumb Code    34  stm32f4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage)
    i.TIM_TI2_ConfigInputStage               0x080053be   Section        0  stm32f4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage)
    TIM_TI2_ConfigInputStage                 0x080053bf   Thumb Code    36  stm32f4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage)
    i.UART4_IRQHandler                       0x080053e4   Section        0  stm32f4xx_it.o(i.UART4_IRQHandler)
    i.UART_DMAAbortOnError                   0x08005410   Section        0  stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError)
    UART_DMAAbortOnError                     0x08005411   Thumb Code    14  stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError)
    i.UART_DMAError                          0x0800541e   Section        0  stm32f4xx_hal_uart.o(i.UART_DMAError)
    UART_DMAError                            0x0800541f   Thumb Code    74  stm32f4xx_hal_uart.o(i.UART_DMAError)
    i.UART_DMAReceiveCplt                    0x08005468   Section        0  stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt)
    UART_DMAReceiveCplt                      0x08005469   Thumb Code   134  stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt)
    i.UART_DMARxHalfCplt                     0x080054ee   Section        0  stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt)
    UART_DMARxHalfCplt                       0x080054ef   Thumb Code    30  stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt)
    i.UART_EndRxTransfer                     0x0800550c   Section        0  stm32f4xx_hal_uart.o(i.UART_EndRxTransfer)
    UART_EndRxTransfer                       0x0800550d   Thumb Code    78  stm32f4xx_hal_uart.o(i.UART_EndRxTransfer)
    i.UART_EndTxTransfer                     0x0800555a   Section        0  stm32f4xx_hal_uart.o(i.UART_EndTxTransfer)
    UART_EndTxTransfer                       0x0800555b   Thumb Code    28  stm32f4xx_hal_uart.o(i.UART_EndTxTransfer)
    i.UART_Receive_IT                        0x08005576   Section        0  stm32f4xx_hal_uart.o(i.UART_Receive_IT)
    UART_Receive_IT                          0x08005577   Thumb Code   194  stm32f4xx_hal_uart.o(i.UART_Receive_IT)
    i.UART_SetConfig                         0x08005638   Section        0  stm32f4xx_hal_uart.o(i.UART_SetConfig)
    UART_SetConfig                           0x08005639   Thumb Code   258  stm32f4xx_hal_uart.o(i.UART_SetConfig)
    i.UART_Start_Receive_DMA                 0x08005744   Section        0  stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA)
    i.UART_WaitOnFlagUntilTimeout            0x080057e4   Section        0  stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout)
    UART_WaitOnFlagUntilTimeout              0x080057e5   Thumb Code   114  stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout)
    i.USART1_IRQHandler                      0x08005858   Section        0  stm32f4xx_it.o(i.USART1_IRQHandler)
    i.USART2_IRQHandler                      0x08005864   Section        0  stm32f4xx_it.o(i.USART2_IRQHandler)
    i.USART3_IRQHandler                      0x08005890   Section        0  stm32f4xx_it.o(i.USART3_IRQHandler)
    i.USART6_IRQHandler                      0x0800589c   Section        0  stm32f4xx_it.o(i.USART6_IRQHandler)
    i.UsageFault_Handler                     0x080058c8   Section        0  stm32f4xx_it.o(i.UsageFault_Handler)
    i.__ARM_fpclassify                       0x080058ca   Section        0  fpclassify.o(i.__ARM_fpclassify)
    i.__NVIC_SetPriority                     0x080058fa   Section        0  stm32f4xx_hal_cortex.o(i.__NVIC_SetPriority)
    __NVIC_SetPriority                       0x080058fb   Thumb Code    32  stm32f4xx_hal_cortex.o(i.__NVIC_SetPriority)
    i._is_digit                              0x0800591a   Section        0  __printf_wp.o(i._is_digit)
    i.abs_limit                              0x08005928   Section        0  mypid.o(i.abs_limit)
    i.calc_motor_angle                       0x08005954   Section        0  uart_bsp.o(i.calc_motor_angle)
    i.calc_relative_angle                    0x08005980   Section        0  uart_bsp.o(i.calc_relative_angle)
    i.main                                   0x080059c8   Section        0  main.o(i.main)
    i.my_printf                              0x08005a70   Section        0  uart_bsp.o(i.my_printf)
    i.parse_x_motor_data                     0x08005aa4   Section        0  uart_bsp.o(i.parse_x_motor_data)
    i.parse_y_motor_data                     0x08005db8   Section        0  uart_bsp.o(i.parse_y_motor_data)
    i.pi_parse_data                          0x080060cc   Section        0  pi_bsp.o(i.pi_parse_data)
    i.pi_proc                                0x080061b0   Section        0  pi_bsp.o(i.pi_proc)
    i.pid_calc                               0x08006210   Section        0  mypid.o(i.pid_calc)
    i.pid_param_init                         0x080063a4   Section        0  mypid.o(i.pid_param_init)
    pid_param_init                           0x080063a5   Thumb Code    14  mypid.o(i.pid_param_init)
    i.pid_reset                              0x080063b4   Section        0  mypid.o(i.pid_reset)
    pid_reset                                0x080063b5   Thumb Code    26  mypid.o(i.pid_reset)
    i.rt_ringbuffer_data_len                 0x080063d4   Section        0  ringbuffer.o(i.rt_ringbuffer_data_len)
    i.rt_ringbuffer_get                      0x08006404   Section        0  ringbuffer.o(i.rt_ringbuffer_get)
    i.rt_ringbuffer_init                     0x08006478   Section        0  ringbuffer.o(i.rt_ringbuffer_init)
    i.rt_ringbuffer_put                      0x080064a8   Section        0  ringbuffer.o(i.rt_ringbuffer_put)
    i.rt_ringbuffer_status                   0x08006520   Section        0  ringbuffer.o(i.rt_ringbuffer_status)
    rt_ringbuffer_status                     0x08006521   Thumb Code    32  ringbuffer.o(i.rt_ringbuffer_status)
    i.save_initial_position                  0x08006540   Section        0  uart_bsp.o(i.save_initial_position)
    i.schedule_init                          0x08006594   Section        0  schedule.o(i.schedule_init)
    i.schedule_run                           0x080065a0   Section        0  schedule.o(i.schedule_run)
    i.uart_proc                              0x080065dc   Section        0  uart_bsp.o(i.uart_proc)
    locale$$code                             0x080067a4   Section       44  lc_numeric_c.o(locale$$code)
    locale$$code                             0x080067d0   Section       44  lc_ctype_c.o(locale$$code)
    x$fpl$dretinf                            0x080067fc   Section       12  dretinf.o(x$fpl$dretinf)
    $v0                                      0x080067fc   Number         0  dretinf.o(x$fpl$dretinf)
    x$fpl$f2d                                0x08006808   Section       86  f2d.o(x$fpl$f2d)
    $v0                                      0x08006808   Number         0  f2d.o(x$fpl$f2d)
    x$fpl$fnaninf                            0x0800685e   Section      140  fnaninf.o(x$fpl$fnaninf)
    $v0                                      0x0800685e   Number         0  fnaninf.o(x$fpl$fnaninf)
    x$fpl$fpinit                             0x080068ea   Section       10  fpinit.o(x$fpl$fpinit)
    $v0                                      0x080068ea   Number         0  fpinit.o(x$fpl$fpinit)
    x$fpl$printf1                            0x080068f4   Section        4  printf1.o(x$fpl$printf1)
    $v0                                      0x080068f4   Number         0  printf1.o(x$fpl$printf1)
    x$fpl$printf2                            0x080068f8   Section        4  printf2.o(x$fpl$printf2)
    $v0                                      0x080068f8   Number         0  printf2.o(x$fpl$printf2)
    .constdata                               0x080068fc   Section        8  stm32f4xx_hal_dma.o(.constdata)
    x$fpl$usenofp                            0x080068fc   Section        0  usenofp.o(x$fpl$usenofp)
    flagBitshiftOffset                       0x080068fc   Data           8  stm32f4xx_hal_dma.o(.constdata)
    .constdata                               0x08006904   Section       16  system_stm32f4xx.o(.constdata)
    .constdata                               0x08006914   Section        8  system_stm32f4xx.o(.constdata)
    .constdata                               0x0800691c   Section       17  __printf_flags_ss_wp.o(.constdata)
    maptable                                 0x0800691c   Data          17  __printf_flags_ss_wp.o(.constdata)
    .constdata                               0x08006930   Section        8  _printf_wctomb.o(.constdata)
    initial_mbstate                          0x08006930   Data           8  _printf_wctomb.o(.constdata)
    .constdata                               0x08006938   Section       40  _printf_hex_int_ll_ptr.o(.constdata)
    uc_hextab                                0x08006938   Data          20  _printf_hex_int_ll_ptr.o(.constdata)
    lc_hextab                                0x0800694c   Data          20  _printf_hex_int_ll_ptr.o(.constdata)
    .constdata                               0x08006960   Section       38  _printf_fp_hex.o(.constdata)
    lc_hextab                                0x08006960   Data          19  _printf_fp_hex.o(.constdata)
    uc_hextab                                0x08006973   Data          19  _printf_fp_hex.o(.constdata)
    .constdata                               0x08006988   Section      148  bigflt0.o(.constdata)
    tenpwrs_x                                0x08006988   Data          60  bigflt0.o(.constdata)
    tenpwrs_i                                0x080069c4   Data          64  bigflt0.o(.constdata)
    .conststring                             0x08006a1c   Section      135  uart_bsp.o(.conststring)
    locale$$data                             0x08006ac4   Section       28  lc_numeric_c.o(locale$$data)
    __lcnum_c_name                           0x08006ac8   Data           2  lc_numeric_c.o(locale$$data)
    __lcnum_c_start                          0x08006ad0   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_point                          0x08006adc   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_thousands                      0x08006ade   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_grouping                       0x08006adf   Data           0  lc_numeric_c.o(locale$$data)
    locale$$data                             0x08006ae0   Section      272  lc_ctype_c.o(locale$$data)
    __lcnum_c_end                            0x08006ae0   Data           0  lc_numeric_c.o(locale$$data)
    __lcctype_c_name                         0x08006ae4   Data           2  lc_ctype_c.o(locale$$data)
    __lcctype_c_start                        0x08006aec   Data           0  lc_ctype_c.o(locale$$data)
    __lcctype_c_end                          0x08006bf0   Data           0  lc_ctype_c.o(locale$$data)
    .data                                    0x20000000   Section       12  stm32f4xx_hal.o(.data)
    .data                                    0x2000000c   Section        4  system_stm32f4xx.o(.data)
    .data                                    0x20000010   Section       22  oled.o(.data)
    .data                                    0x20000028   Section       28  schedule.o(.data)
    schedule_task                            0x2000002c   Data          24  schedule.o(.data)
    .data                                    0x20000044   Section       44  uart_bsp.o(.data)
    line_buffer_idx                          0x2000004c   Data           4  uart_bsp.o(.data)
    .data                                    0x20000070   Section       32  pi_bsp.o(.data)
    .bss                                     0x20000090   Section      168  i2c.o(.bss)
    .bss                                     0x20000138   Section      216  tim.o(.bss)
    .bss                                     0x20000210   Section      912  usart.o(.bss)
    .bss                                     0x200005a0   Section      356  uart_bsp.o(.bss)
    line_buffer                              0x20000684   Data         128  uart_bsp.o(.bss)
    .bss                                     0x20000704   Section       64  uart_bsp.o(.bss)
    .bss                                     0x20000744   Section       64  uart_bsp.o(.bss)
    .bss                                     0x20000784   Section       64  uart_bsp.o(.bss)
    .bss                                     0x200007c4   Section      480  mypid.o(.bss)
    .bss                                     0x200009a4   Section       96  libspace.o(.bss)
    HEAP                                     0x20000a08   Section      512  startup_stm32f407xx.o(HEAP)
    Heap_Mem                                 0x20000a08   Data         512  startup_stm32f407xx.o(HEAP)
    STACK                                    0x20000c08   Section     1024  startup_stm32f407xx.o(STACK)
    Stack_Mem                                0x20000c08   Data        1024  startup_stm32f407xx.o(STACK)
    __initial_sp                             0x20001008   Data           0  startup_stm32f407xx.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$E$P$D$K$B$S$7EM$VFPi3$EXTD16$VFPS$VFMA$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEX$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  printf_stubs.o ABSOLUTE
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_fini_                               - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    __user_heap_extent                        - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _mutex_acquire                            - Undefined Weak Reference
    _mutex_free                               - Undefined Weak Reference
    _mutex_release                            - Undefined Weak Reference
    _printf_mbtowc                            - Undefined Weak Reference
    _printf_wc                                - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _scanf_longlong                           - Undefined Weak Reference
    _scanf_mbtowc                             - Undefined Weak Reference
    _scanf_real                               - Undefined Weak Reference
    _scanf_string                             - Undefined Weak Reference
    _scanf_wctomb                             - Undefined Weak Reference
    _scanf_wstring                            - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    __Vectors_Size                           0x00000188   Number         0  startup_stm32f407xx.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f407xx.o(RESET)
    __Vectors_End                            0x08000188   Data           0  startup_stm32f407xx.o(RESET)
    __main                                   0x08000189   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x08000191   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x08000191   Thumb Code    44  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x08000191   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_null                       0x0800019f   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_copy                       0x080001c5   Thumb Code    26  __scatter_copy.o(!!handler_copy)
    __scatterload_zeroinit                   0x080001e1   Thumb Code    28  __scatter_zi.o(!!handler_zi)
    _printf_n                                0x080001fd   Thumb Code     0  _printf_n.o(.ARM.Collect$$_printf_percent$$00000001)
    _printf_percent                          0x080001fd   Thumb Code     0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    _printf_p                                0x08000203   Thumb Code     0  _printf_p.o(.ARM.Collect$$_printf_percent$$00000002)
    _printf_f                                0x08000209   Thumb Code     0  _printf_f.o(.ARM.Collect$$_printf_percent$$00000003)
    _printf_e                                0x0800020f   Thumb Code     0  _printf_e.o(.ARM.Collect$$_printf_percent$$00000004)
    _printf_g                                0x08000215   Thumb Code     0  _printf_g.o(.ARM.Collect$$_printf_percent$$00000005)
    _printf_a                                0x0800021b   Thumb Code     0  _printf_a.o(.ARM.Collect$$_printf_percent$$00000006)
    _printf_ll                               0x08000221   Thumb Code     0  _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007)
    _printf_i                                0x0800022b   Thumb Code     0  _printf_i.o(.ARM.Collect$$_printf_percent$$00000008)
    _printf_d                                0x08000231   Thumb Code     0  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    _printf_u                                0x08000237   Thumb Code     0  _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A)
    _printf_o                                0x0800023d   Thumb Code     0  _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B)
    _printf_x                                0x08000243   Thumb Code     0  _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C)
    _printf_lli                              0x08000249   Thumb Code     0  _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D)
    _printf_lld                              0x0800024f   Thumb Code     0  _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E)
    _printf_llu                              0x08000255   Thumb Code     0  _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F)
    _printf_llo                              0x0800025b   Thumb Code     0  _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010)
    _printf_llx                              0x08000261   Thumb Code     0  _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011)
    _printf_l                                0x08000267   Thumb Code     0  _printf_l.o(.ARM.Collect$$_printf_percent$$00000012)
    _printf_c                                0x08000271   Thumb Code     0  _printf_c.o(.ARM.Collect$$_printf_percent$$00000013)
    _printf_s                                0x08000277   Thumb Code     0  _printf_s.o(.ARM.Collect$$_printf_percent$$00000014)
    _printf_lc                               0x0800027d   Thumb Code     0  _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015)
    _printf_ls                               0x08000283   Thumb Code     0  _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016)
    _printf_percent_end                      0x08000289   Thumb Code     0  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    __rt_lib_init                            0x0800028d   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_fp_1                       0x0800028f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000001)
    __rt_lib_init_heap_1                     0x08000293   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    __rt_lib_init_lc_common                  0x08000293   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000F)
    __rt_lib_init_preinit_1                  0x08000293   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    __rt_lib_init_rand_1                     0x08000293   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_init_user_alloc_1               0x08000293   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_init_lc_collate_1               0x08000299   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    __rt_lib_init_lc_ctype_2                 0x08000299   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000012)
    __rt_lib_init_lc_ctype_1                 0x080002a5   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_monetary_1              0x080002a5   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_numeric_2               0x080002a5   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000016)
    __rt_lib_init_alloca_1                   0x080002af   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_argv_1                     0x080002af   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    __rt_lib_init_atexit_1                   0x080002af   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_clock_1                    0x080002af   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_cpp_1                      0x080002af   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_exceptions_1               0x080002af   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_fp_trap_1                  0x080002af   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_getenv_1                   0x080002af   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_lc_numeric_1               0x080002af   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_time_1                  0x080002af   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_return                     0x080002af   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000033)
    __rt_lib_init_signal_1                   0x080002af   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_stdio_1                    0x080002af   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_shutdown                        0x080002b1   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_cpp_1                  0x080002b3   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    __rt_lib_shutdown_fini_1                 0x080002b3   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    __rt_lib_shutdown_fp_trap_1              0x080002b3   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    __rt_lib_shutdown_heap_1                 0x080002b3   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000011)
    __rt_lib_shutdown_return                 0x080002b3   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000012)
    __rt_lib_shutdown_signal_1               0x080002b3   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    __rt_lib_shutdown_stdio_1                0x080002b3   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    __rt_lib_shutdown_user_alloc_1           0x080002b3   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    __rt_entry                               0x080002b5   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x080002b5   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x080002b5   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    __rt_entry_li                            0x080002bb   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x080002bb   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    __rt_entry_main                          0x080002bf   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x080002bf   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x080002c7   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x080002c9   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    __rt_exit_prels_1                        0x080002c9   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x080002cd   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    Reset_Handler                            0x080002d5   Thumb Code     8  startup_stm32f407xx.o(.text)
    ADC_IRQHandler                           0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_RX0_IRQHandler                      0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_RX1_IRQHandler                      0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_SCE_IRQHandler                      0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_TX_IRQHandler                       0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_RX0_IRQHandler                      0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_RX1_IRQHandler                      0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_SCE_IRQHandler                      0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_TX_IRQHandler                       0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    DCMI_IRQHandler                          0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream0_IRQHandler                  0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream1_IRQHandler                  0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream3_IRQHandler                  0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream4_IRQHandler                  0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream6_IRQHandler                  0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream7_IRQHandler                  0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream0_IRQHandler                  0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream2_IRQHandler                  0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream3_IRQHandler                  0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream4_IRQHandler                  0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream5_IRQHandler                  0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream6_IRQHandler                  0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream7_IRQHandler                  0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    ETH_IRQHandler                           0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    ETH_WKUP_IRQHandler                      0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI0_IRQHandler                         0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI15_10_IRQHandler                     0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI1_IRQHandler                         0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI2_IRQHandler                         0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI3_IRQHandler                         0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI4_IRQHandler                         0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI9_5_IRQHandler                       0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    FLASH_IRQHandler                         0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    FMC_IRQHandler                           0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    FPU_IRQHandler                           0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    HASH_RNG_IRQHandler                      0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C1_ER_IRQHandler                       0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C1_EV_IRQHandler                       0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C2_ER_IRQHandler                       0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C2_EV_IRQHandler                       0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C3_ER_IRQHandler                       0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C3_EV_IRQHandler                       0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_FS_IRQHandler                        0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_FS_WKUP_IRQHandler                   0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_EP1_IN_IRQHandler                 0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_EP1_OUT_IRQHandler                0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_IRQHandler                        0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_WKUP_IRQHandler                   0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    PVD_IRQHandler                           0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    RCC_IRQHandler                           0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    RTC_Alarm_IRQHandler                     0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    RTC_WKUP_IRQHandler                      0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    SDIO_IRQHandler                          0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    SPI1_IRQHandler                          0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    SPI2_IRQHandler                          0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    SPI3_IRQHandler                          0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    TAMP_STAMP_IRQHandler                    0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_BRK_TIM9_IRQHandler                 0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_CC_IRQHandler                       0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_TRG_COM_TIM11_IRQHandler            0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_UP_TIM10_IRQHandler                 0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM2_IRQHandler                          0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM3_IRQHandler                          0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM4_IRQHandler                          0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM5_IRQHandler                          0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM6_DAC_IRQHandler                      0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM7_IRQHandler                          0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_BRK_TIM12_IRQHandler                0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_CC_IRQHandler                       0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_TRG_COM_TIM14_IRQHandler            0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_UP_TIM13_IRQHandler                 0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    UART5_IRQHandler                         0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    WWDG_IRQHandler                          0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    __user_initial_stackheap                 0x080002f1   Thumb Code     0  startup_stm32f407xx.o(.text)
    __aeabi_uldivmod                         0x08000315   Thumb Code     0  lludivv7m.o(.text)
    _ll_udiv                                 0x08000315   Thumb Code   238  lludivv7m.o(.text)
    vsnprintf                                0x08000405   Thumb Code    48  vsnprintf.o(.text)
    _printf_str                              0x08000439   Thumb Code    82  _printf_str.o(.text)
    __printf                                 0x0800048d   Thumb Code   388  __printf_flags_ss_wp.o(.text)
    __0sscanf                                0x08000615   Thumb Code    52  __0sscanf.o(.text)
    _scanf_int                               0x08000651   Thumb Code   332  _scanf_int.o(.text)
    abort                                    0x0800079d   Thumb Code    22  abort.o(.text)
    strncmp                                  0x080007b3   Thumb Code   150  strncmp.o(.text)
    __aeabi_memcpy                           0x08000849   Thumb Code     0  rt_memcpy_v6.o(.text)
    __rt_memcpy                              0x08000849   Thumb Code   138  rt_memcpy_v6.o(.text)
    _memcpy_lastbytes                        0x080008af   Thumb Code     0  rt_memcpy_v6.o(.text)
    __aeabi_memclr4                          0x080008d3   Thumb Code     0  rt_memclr_w.o(.text)
    __aeabi_memclr8                          0x080008d3   Thumb Code     0  rt_memclr_w.o(.text)
    __rt_memclr_w                            0x080008d3   Thumb Code    78  rt_memclr_w.o(.text)
    _memset_w                                0x080008d7   Thumb Code     0  rt_memclr_w.o(.text)
    __use_two_region_memory                  0x08000921   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow                         0x08000923   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand                         0x08000925   Thumb Code     2  heapauxi.o(.text)
    _sys_exit                                0x08000929   Thumb Code     8  sys_exit.o(.text)
    _printf_pre_padding                      0x08000935   Thumb Code    44  _printf_pad.o(.text)
    _printf_post_padding                     0x08000961   Thumb Code    34  _printf_pad.o(.text)
    _printf_truncate_signed                  0x08000983   Thumb Code    18  _printf_truncate.o(.text)
    _printf_truncate_unsigned                0x08000995   Thumb Code    18  _printf_truncate.o(.text)
    _printf_int_dec                          0x080009a9   Thumb Code   104  _printf_dec.o(.text)
    _printf_charcount                        0x08000a21   Thumb Code    40  _printf_charcount.o(.text)
    _printf_char_common                      0x08000a53   Thumb Code    32  _printf_char_common.o(.text)
    _sputc                                   0x08000a79   Thumb Code    10  _sputc.o(.text)
    _snputc                                  0x08000a83   Thumb Code    16  _snputc.o(.text)
    _printf_cs_common                        0x08000a93   Thumb Code    20  _printf_char.o(.text)
    _printf_char                             0x08000aa7   Thumb Code    16  _printf_char.o(.text)
    _printf_string                           0x08000ab7   Thumb Code     8  _printf_char.o(.text)
    _printf_wctomb                           0x08000ac1   Thumb Code   182  _printf_wctomb.o(.text)
    _printf_longlong_dec                     0x08000b7d   Thumb Code   108  _printf_longlong_dec.o(.text)
    _printf_longlong_oct                     0x08000bf9   Thumb Code    66  _printf_oct_int_ll.o(.text)
    _printf_int_oct                          0x08000c3b   Thumb Code    24  _printf_oct_int_ll.o(.text)
    _printf_ll_oct                           0x08000c53   Thumb Code    12  _printf_oct_int_ll.o(.text)
    _printf_longlong_hex                     0x08000c69   Thumb Code    86  _printf_hex_int_ll_ptr.o(.text)
    _printf_int_hex                          0x08000cbf   Thumb Code    28  _printf_hex_int_ll_ptr.o(.text)
    _printf_ll_hex                           0x08000cdb   Thumb Code    12  _printf_hex_int_ll_ptr.o(.text)
    _printf_hex_ptr                          0x08000ce7   Thumb Code    18  _printf_hex_int_ll_ptr.o(.text)
    _chval                                   0x08000cfd   Thumb Code    28  _chval.o(.text)
    __vfscanf_char                           0x08000d25   Thumb Code    24  scanf_char.o(.text)
    _sgetc                                   0x08000d45   Thumb Code    30  _sgetc.o(.text)
    _sbackspace                              0x08000d63   Thumb Code    34  _sgetc.o(.text)
    __rt_SIGABRT                             0x08000d85   Thumb Code    14  defsig_abrt_outer.o(.text)
    __aeabi_memcpy4                          0x08000d93   Thumb Code     0  rt_memcpy_w.o(.text)
    __aeabi_memcpy8                          0x08000d93   Thumb Code     0  rt_memcpy_w.o(.text)
    __rt_memcpy_w                            0x08000d93   Thumb Code   100  rt_memcpy_w.o(.text)
    _memcpy_lastbytes_aligned                0x08000ddb   Thumb Code     0  rt_memcpy_w.o(.text)
    __I$use$semihosting                      0x08000df7   Thumb Code     0  use_no_semi.o(.text)
    __use_no_semihosting_swi                 0x08000df7   Thumb Code     2  use_no_semi.o(.text)
    __semihosting_library_function           0x08000df9   Thumb Code     0  indicate_semi.o(.text)
    _ll_udiv10                               0x08000df9   Thumb Code   138  lludiv10.o(.text)
    isspace                                  0x08000e83   Thumb Code    18  isspace.o(.text)
    _printf_int_common                       0x08000e95   Thumb Code   178  _printf_intcommon.o(.text)
    __lib_sel_fp_printf                      0x08000f47   Thumb Code     2  _printf_fp_dec.o(.text)
    _printf_fp_dec_real                      0x080010f9   Thumb Code   620  _printf_fp_dec.o(.text)
    _printf_fp_hex_real                      0x08001365   Thumb Code   756  _printf_fp_hex.o(.text)
    _printf_lcs_common                       0x08001661   Thumb Code    20  _printf_wchar.o(.text)
    _printf_wchar                            0x08001675   Thumb Code    16  _printf_wchar.o(.text)
    _printf_wstring                          0x08001685   Thumb Code     8  _printf_wchar.o(.text)
    __vfscanf                                0x0800168d   Thumb Code   880  _scanf.o(.text)
    _wcrtomb                                 0x08001a01   Thumb Code    64  _wcrtomb.o(.text)
    __sig_exit                               0x08001a41   Thumb Code    10  defsig_exit.o(.text)
    __rt_SIGABRT_inner                       0x08001a4d   Thumb Code    14  defsig_abrt_inner.o(.text)
    __user_libspace                          0x08001a7d   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x08001a7d   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x08001a7d   Thumb Code     0  libspace.o(.text)
    __user_setup_stackheap                   0x08001a85   Thumb Code    74  sys_stackheap_outer.o(.text)
    __rt_ctype_table                         0x08001ad1   Thumb Code    16  rt_ctype_table.o(.text)
    __rt_locale                              0x08001ae1   Thumb Code     8  rt_locale_intlibspace.o(.text)
    _printf_fp_infnan                        0x08001ae9   Thumb Code   112  _printf_fp_infnan.o(.text)
    _btod_etento                             0x08001b69   Thumb Code   224  bigflt0.o(.text)
    exit                                     0x08001c4d   Thumb Code    18  exit.o(.text)
    __default_signal_display                 0x08001c5f   Thumb Code    50  defsig_general.o(.text)
    _ttywrch                                 0x08001c91   Thumb Code    14  sys_wrch.o(.text)
    strcmp                                   0x08001ca1   Thumb Code   128  strcmpv7m.o(.text)
    _btod_d2e                                0x08001d21   Thumb Code    62  btod.o(CL$$btod_d2e)
    _d2e_denorm_low                          0x08001d5f   Thumb Code    70  btod.o(CL$$btod_d2e_denorm_low)
    _d2e_norm_op1                            0x08001da5   Thumb Code    96  btod.o(CL$$btod_d2e_norm_op1)
    __btod_div_common                        0x08001e05   Thumb Code   696  btod.o(CL$$btod_div_common)
    _e2e                                     0x0800213d   Thumb Code   220  btod.o(CL$$btod_e2e)
    _btod_ediv                               0x08002219   Thumb Code    42  btod.o(CL$$btod_ediv)
    _btod_emul                               0x08002243   Thumb Code    42  btod.o(CL$$btod_emul)
    __btod_mult_common                       0x0800226d   Thumb Code   580  btod.o(CL$$btod_mult_common)
    BusFault_Handler                         0x080024b1   Thumb Code     2  stm32f4xx_it.o(i.BusFault_Handler)
    DMA1_Stream2_IRQHandler                  0x080024b5   Thumb Code     6  stm32f4xx_it.o(i.DMA1_Stream2_IRQHandler)
    DMA1_Stream5_IRQHandler                  0x080024c1   Thumb Code     6  stm32f4xx_it.o(i.DMA1_Stream5_IRQHandler)
    DMA2_Stream1_IRQHandler                  0x080024cd   Thumb Code     6  stm32f4xx_it.o(i.DMA2_Stream1_IRQHandler)
    DebugMon_Handler                         0x0800257d   Thumb Code     2  stm32f4xx_it.o(i.DebugMon_Handler)
    Emm_V5_En_Control                        0x0800257f   Thumb Code    56  emm_v5.o(i.Emm_V5_En_Control)
    Emm_V5_Parse_Response                    0x080025b7   Thumb Code   492  emm_v5.o(i.Emm_V5_Parse_Response)
    Emm_V5_Read_Sys_Params                   0x080027a3   Thumb Code   142  emm_v5.o(i.Emm_V5_Read_Sys_Params)
    Emm_V5_Reset_CurPos_To_Zero              0x08002831   Thumb Code    48  emm_v5.o(i.Emm_V5_Reset_CurPos_To_Zero)
    Emm_V5_Stop_Now                          0x08002861   Thumb Code    52  emm_v5.o(i.Emm_V5_Stop_Now)
    Emm_V5_Vel_Control                       0x08002895   Thumb Code   122  emm_v5.o(i.Emm_V5_Vel_Control)
    Error_Handler                            0x0800295d   Thumb Code     4  main.o(i.Error_Handler)
    HAL_DMA_Abort                            0x08002961   Thumb Code   146  stm32f4xx_hal_dma.o(i.HAL_DMA_Abort)
    HAL_DMA_Abort_IT                         0x080029f3   Thumb Code    36  stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT)
    HAL_DMA_IRQHandler                       0x08002a19   Thumb Code   412  stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler)
    HAL_DMA_Init                             0x08002bb9   Thumb Code   206  stm32f4xx_hal_dma.o(i.HAL_DMA_Init)
    HAL_DMA_Start_IT                         0x08002c8d   Thumb Code   110  stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT)
    HAL_Delay                                0x08002cfd   Thumb Code    32  stm32f4xx_hal.o(i.HAL_Delay)
    HAL_GPIO_Init                            0x08002d21   Thumb Code   450  stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init)
    HAL_GPIO_WritePin                        0x08002f11   Thumb Code    10  stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin)
    HAL_GetTick                              0x08002f1d   Thumb Code     6  stm32f4xx_hal.o(i.HAL_GetTick)
    HAL_I2C_Init                             0x08002f29   Thumb Code   376  stm32f4xx_hal_i2c.o(i.HAL_I2C_Init)
    HAL_I2C_Mem_Write                        0x080030b1   Thumb Code   294  stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write)
    HAL_I2C_MspInit                          0x080031e1   Thumb Code   154  i2c.o(i.HAL_I2C_MspInit)
    HAL_IncTick                              0x0800328d   Thumb Code    12  stm32f4xx_hal.o(i.HAL_IncTick)
    HAL_Init                                 0x0800329d   Thumb Code    48  stm32f4xx_hal.o(i.HAL_Init)
    HAL_InitTick                             0x080032d1   Thumb Code    54  stm32f4xx_hal.o(i.HAL_InitTick)
    HAL_MspInit                              0x08003311   Thumb Code    42  stm32f4xx_hal_msp.o(i.HAL_MspInit)
    HAL_NVIC_EnableIRQ                       0x08003341   Thumb Code    26  stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ)
    HAL_NVIC_SetPriority                     0x0800335d   Thumb Code    60  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    HAL_NVIC_SetPriorityGrouping             0x0800339d   Thumb Code    26  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    HAL_RCC_ClockConfig                      0x080033c1   Thumb Code   288  stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    HAL_RCC_GetPCLK1Freq                     0x080034f5   Thumb Code    20  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq)
    HAL_RCC_GetPCLK2Freq                     0x08003515   Thumb Code    20  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq)
    HAL_RCC_GetSysClockFreq                  0x08003535   Thumb Code    88  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    HAL_RCC_OscConfig                        0x08003599   Thumb Code   856  stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    HAL_SYSTICK_Config                       0x08003905   Thumb Code    40  stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config)
    HAL_TIMEx_ConfigBreakDeadTime            0x0800392d   Thumb Code    84  stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigBreakDeadTime)
    HAL_TIMEx_MasterConfigSynchronization    0x08003981   Thumb Code   116  stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization)
    HAL_TIM_Base_Init                        0x08003a11   Thumb Code    90  stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init)
    HAL_TIM_Base_MspInit                     0x08003a6d   Thumb Code    30  tim.o(i.HAL_TIM_Base_MspInit)
    HAL_TIM_ConfigClockSource                0x08003a95   Thumb Code   220  stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource)
    HAL_TIM_Encoder_Init                     0x08003b71   Thumb Code   164  stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init)
    HAL_TIM_Encoder_MspInit                  0x08003c15   Thumb Code   142  tim.o(i.HAL_TIM_Encoder_MspInit)
    HAL_TIM_MspPostInit                      0x08003cb9   Thumb Code    72  tim.o(i.HAL_TIM_MspPostInit)
    HAL_TIM_PWM_ConfigChannel                0x08003d0d   Thumb Code   204  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel)
    HAL_TIM_PWM_Init                         0x08003dd9   Thumb Code    90  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init)
    HAL_TIM_PWM_MspInit                      0x08003e33   Thumb Code     2  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_MspInit)
    HAL_UARTEx_ReceiveToIdle_DMA             0x08003e35   Thumb Code    74  stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA)
    HAL_UARTEx_RxEventCallback               0x08003e81   Thumb Code    98  usart.o(i.HAL_UARTEx_RxEventCallback)
    HAL_UART_ErrorCallback                   0x08003f01   Thumb Code     2  stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback)
    HAL_UART_IRQHandler                      0x08003f05   Thumb Code   636  stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler)
    HAL_UART_Init                            0x08004185   Thumb Code   100  stm32f4xx_hal_uart.o(i.HAL_UART_Init)
    HAL_UART_MspInit                         0x080041e9   Thumb Code   674  usart.o(i.HAL_UART_MspInit)
    HAL_UART_RxCpltCallback                  0x080044c1   Thumb Code     2  stm32f4xx_hal_uart.o(i.HAL_UART_RxCpltCallback)
    HAL_UART_RxHalfCpltCallback              0x080044c3   Thumb Code     2  stm32f4xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback)
    HAL_UART_Transmit                        0x080044c5   Thumb Code   160  stm32f4xx_hal_uart.o(i.HAL_UART_Transmit)
    HAL_UART_TxCpltCallback                  0x08004565   Thumb Code     2  stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback)
    HardFault_Handler                        0x08004567   Thumb Code     2  stm32f4xx_it.o(i.HardFault_Handler)
    MX_DMA_Init                              0x0800483d   Thumb Code    88  dma.o(i.MX_DMA_Init)
    MX_GPIO_Init                             0x08004899   Thumb Code   268  gpio.o(i.MX_GPIO_Init)
    MX_I2C1_Init                             0x080049b9   Thumb Code    50  i2c.o(i.MX_I2C1_Init)
    MX_I2C2_Init                             0x080049f9   Thumb Code    50  i2c.o(i.MX_I2C2_Init)
    MX_TIM1_Init                             0x08004a39   Thumb Code   206  tim.o(i.MX_TIM1_Init)
    MX_TIM3_Init                             0x08004b11   Thumb Code    98  tim.o(i.MX_TIM3_Init)
    MX_TIM4_Init                             0x08004b7d   Thumb Code    98  tim.o(i.MX_TIM4_Init)
    MX_UART4_Init                            0x08004be9   Thumb Code    66  usart.o(i.MX_UART4_Init)
    MX_UART5_Init                            0x08004c39   Thumb Code    48  usart.o(i.MX_UART5_Init)
    MX_USART1_UART_Init                      0x08004c71   Thumb Code    48  usart.o(i.MX_USART1_UART_Init)
    MX_USART2_UART_Init                      0x08004ca9   Thumb Code    64  usart.o(i.MX_USART2_UART_Init)
    MX_USART3_UART_Init                      0x08004cf9   Thumb Code    48  usart.o(i.MX_USART3_UART_Init)
    MX_USART6_UART_Init                      0x08004d31   Thumb Code    64  usart.o(i.MX_USART6_UART_Init)
    MemManage_Handler                        0x08004d81   Thumb Code     2  stm32f4xx_it.o(i.MemManage_Handler)
    NMI_Handler                              0x08004d83   Thumb Code     2  stm32f4xx_it.o(i.NMI_Handler)
    OLED_Clear                               0x08004d85   Thumb Code    52  oled.o(i.OLED_Clear)
    OLED_Init                                0x08004db9   Thumb Code    42  oled.o(i.OLED_Init)
    OLED_Set_Position                        0x08004de9   Thumb Code    34  oled.o(i.OLED_Set_Position)
    OLED_Write_cmd                           0x08004e0d   Thumb Code    32  oled.o(i.OLED_Write_cmd)
    OLED_Write_data                          0x08004e31   Thumb Code    32  oled.o(i.OLED_Write_data)
    PID_INIT                                 0x08004e55   Thumb Code   182  mypid.o(i.PID_INIT)
    PID_struct_init                          0x08004f25   Thumb Code    60  mypid.o(i.PID_struct_init)
    PendSV_Handler                           0x08004f69   Thumb Code     2  stm32f4xx_it.o(i.PendSV_Handler)
    SVC_Handler                              0x08004f6b   Thumb Code     2  stm32f4xx_it.o(i.SVC_Handler)
    Step_Motor_Init                          0x08004f6d   Thumb Code    34  step_motor_bsp.o(i.Step_Motor_Init)
    Step_Motor_Set_Speed_my                  0x08004f99   Thumb Code   178  step_motor_bsp.o(i.Step_Motor_Set_Speed_my)
    Step_Motor_Stop                          0x0800505d   Thumb Code    26  step_motor_bsp.o(i.Step_Motor_Stop)
    SysTick_Handler                          0x08005081   Thumb Code     4  stm32f4xx_it.o(i.SysTick_Handler)
    SystemClock_Config                       0x08005085   Thumb Code   136  main.o(i.SystemClock_Config)
    SystemInit                               0x08005115   Thumb Code    12  system_stm32f4xx.o(i.SystemInit)
    TIM_Base_SetConfig                       0x08005125   Thumb Code   164  stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig)
    TIM_ETR_SetConfig                        0x080051f5   Thumb Code    20  stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig)
    TIM_OC2_SetConfig                        0x08005279   Thumb Code    98  stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig)
    UART4_IRQHandler                         0x080053e5   Thumb Code    32  stm32f4xx_it.o(i.UART4_IRQHandler)
    UART_Start_Receive_DMA                   0x08005745   Thumb Code   146  stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA)
    USART1_IRQHandler                        0x08005859   Thumb Code     6  stm32f4xx_it.o(i.USART1_IRQHandler)
    USART2_IRQHandler                        0x08005865   Thumb Code    32  stm32f4xx_it.o(i.USART2_IRQHandler)
    USART3_IRQHandler                        0x08005891   Thumb Code     6  stm32f4xx_it.o(i.USART3_IRQHandler)
    USART6_IRQHandler                        0x0800589d   Thumb Code    32  stm32f4xx_it.o(i.USART6_IRQHandler)
    UsageFault_Handler                       0x080058c9   Thumb Code     2  stm32f4xx_it.o(i.UsageFault_Handler)
    __ARM_fpclassify                         0x080058cb   Thumb Code    48  fpclassify.o(i.__ARM_fpclassify)
    _is_digit                                0x0800591b   Thumb Code    14  __printf_wp.o(i._is_digit)
    abs_limit                                0x08005929   Thumb Code    42  mypid.o(i.abs_limit)
    calc_motor_angle                         0x08005955   Thumb Code    36  uart_bsp.o(i.calc_motor_angle)
    calc_relative_angle                      0x08005981   Thumb Code    62  uart_bsp.o(i.calc_relative_angle)
    main                                     0x080059c9   Thumb Code   134  main.o(i.main)
    my_printf                                0x08005a71   Thumb Code    52  uart_bsp.o(i.my_printf)
    parse_x_motor_data                       0x08005aa5   Thumb Code   326  uart_bsp.o(i.parse_x_motor_data)
    parse_y_motor_data                       0x08005db9   Thumb Code   326  uart_bsp.o(i.parse_y_motor_data)
    pi_parse_data                            0x080060cd   Thumb Code   122  pi_bsp.o(i.pi_parse_data)
    pi_proc                                  0x080061b1   Thumb Code    84  pi_bsp.o(i.pi_proc)
    pid_calc                                 0x08006211   Thumb Code   390  mypid.o(i.pid_calc)
    rt_ringbuffer_data_len                   0x080063d5   Thumb Code    48  ringbuffer.o(i.rt_ringbuffer_data_len)
    rt_ringbuffer_get                        0x08006405   Thumb Code   116  ringbuffer.o(i.rt_ringbuffer_get)
    rt_ringbuffer_init                       0x08006479   Thumb Code    48  ringbuffer.o(i.rt_ringbuffer_init)
    rt_ringbuffer_put                        0x080064a9   Thumb Code   120  ringbuffer.o(i.rt_ringbuffer_put)
    save_initial_position                    0x08006541   Thumb Code    44  uart_bsp.o(i.save_initial_position)
    schedule_init                            0x08006595   Thumb Code     8  schedule.o(i.schedule_init)
    schedule_run                             0x080065a1   Thumb Code    56  schedule.o(i.schedule_run)
    uart_proc                                0x080065dd   Thumb Code   278  uart_bsp.o(i.uart_proc)
    _get_lc_numeric                          0x080067a5   Thumb Code    44  lc_numeric_c.o(locale$$code)
    _get_lc_ctype                            0x080067d1   Thumb Code    44  lc_ctype_c.o(locale$$code)
    __fpl_dretinf                            0x080067fd   Thumb Code    12  dretinf.o(x$fpl$dretinf)
    __aeabi_f2d                              0x08006809   Thumb Code     0  f2d.o(x$fpl$f2d)
    _f2d                                     0x08006809   Thumb Code    86  f2d.o(x$fpl$f2d)
    __fpl_fnaninf                            0x0800685f   Thumb Code   140  fnaninf.o(x$fpl$fnaninf)
    _fp_init                                 0x080068eb   Thumb Code    10  fpinit.o(x$fpl$fpinit)
    __fplib_config_fpu_vfp                   0x080068f3   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    __fplib_config_pureend_doubles           0x080068f3   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    _printf_fp_dec                           0x080068f5   Thumb Code     4  printf1.o(x$fpl$printf1)
    _printf_fp_hex                           0x080068f9   Thumb Code     4  printf2.o(x$fpl$printf2)
    __I$use$fp                               0x080068fc   Number         0  usenofp.o(x$fpl$usenofp)
    AHBPrescTable                            0x08006904   Data          16  system_stm32f4xx.o(.constdata)
    APBPrescTable                            0x08006914   Data           8  system_stm32f4xx.o(.constdata)
    Region$$Table$$Base                      0x08006aa4   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x08006ac4   Number         0  anon$$obj.o(Region$$Table)
    __ctype                                  0x08006aed   Data           0  lc_ctype_c.o(locale$$data)
    uwTickFreq                               0x20000000   Data           1  stm32f4xx_hal.o(.data)
    uwTickPrio                               0x20000004   Data           4  stm32f4xx_hal.o(.data)
    uwTick                                   0x20000008   Data           4  stm32f4xx_hal.o(.data)
    SystemCoreClock                          0x2000000c   Data           4  system_stm32f4xx.o(.data)
    initcmd1                                 0x20000010   Data          22  oled.o(.data)
    task_num                                 0x20000028   Data           1  schedule.o(.data)
    x_angle_limit_flag                       0x20000044   Data           1  uart_bsp.o(.data)
    y_angle_limit_flag                       0x20000045   Data           1  uart_bsp.o(.data)
    motor_angle_limit_check_enabled          0x20000046   Data           1  uart_bsp.o(.data)
    x_reference_initialized                  0x20000047   Data           1  uart_bsp.o(.data)
    y_reference_initialized                  0x20000048   Data           1  uart_bsp.o(.data)
    x_initial_direction                      0x20000049   Data           1  uart_bsp.o(.data)
    y_initial_direction                      0x2000004a   Data           1  uart_bsp.o(.data)
    initial_position_saved                   0x2000004b   Data           1  uart_bsp.o(.data)
    x_motor_angle                            0x20000050   Data           4  uart_bsp.o(.data)
    y_motor_angle                            0x20000054   Data           4  uart_bsp.o(.data)
    x_reference_position                     0x20000058   Data           4  uart_bsp.o(.data)
    y_reference_position                     0x2000005c   Data           4  uart_bsp.o(.data)
    x_relative_angle                         0x20000060   Data           4  uart_bsp.o(.data)
    y_relative_angle                         0x20000064   Data           4  uart_bsp.o(.data)
    x_initial_position                       0x20000068   Data           4  uart_bsp.o(.data)
    y_initial_position                       0x2000006c   Data           4  uart_bsp.o(.data)
    latest_red_laser_coord                   0x20000070   Data          16  pi_bsp.o(.data)
    latest_green_laser_coord                 0x20000080   Data          16  pi_bsp.o(.data)
    hi2c1                                    0x20000090   Data          84  i2c.o(.bss)
    hi2c2                                    0x200000e4   Data          84  i2c.o(.bss)
    htim1                                    0x20000138   Data          72  tim.o(.bss)
    htim3                                    0x20000180   Data          72  tim.o(.bss)
    htim4                                    0x200001c8   Data          72  tim.o(.bss)
    motor_x_buf                              0x20000210   Data          64  usart.o(.bss)
    motor_y_buf                              0x20000250   Data          64  usart.o(.bss)
    pi_rx_buf                                0x20000290   Data          64  usart.o(.bss)
    huart4                                   0x200002d0   Data          72  usart.o(.bss)
    huart5                                   0x20000318   Data          72  usart.o(.bss)
    huart1                                   0x20000360   Data          72  usart.o(.bss)
    huart2                                   0x200003a8   Data          72  usart.o(.bss)
    huart3                                   0x200003f0   Data          72  usart.o(.bss)
    huart6                                   0x20000438   Data          72  usart.o(.bss)
    hdma_uart4_rx                            0x20000480   Data          96  usart.o(.bss)
    hdma_usart2_rx                           0x200004e0   Data          96  usart.o(.bss)
    hdma_usart6_rx                           0x20000540   Data          96  usart.o(.bss)
    ringbuffer_x                             0x200005a0   Data          12  uart_bsp.o(.bss)
    ringbuffer_y                             0x200005ac   Data          12  uart_bsp.o(.bss)
    ringbuffer_pi                            0x200005b8   Data          12  uart_bsp.o(.bss)
    output_buffer_x                          0x200005c4   Data          64  uart_bsp.o(.bss)
    output_buffer_y                          0x20000604   Data          64  uart_bsp.o(.bss)
    output_buffer_pi                         0x20000644   Data          64  uart_bsp.o(.bss)
    ringbuffer_pool_x                        0x20000704   Data          64  uart_bsp.o(.bss)
    ringbuffer_pool_y                        0x20000744   Data          64  uart_bsp.o(.bss)
    ringbuffer_pool_pi                       0x20000784   Data          64  uart_bsp.o(.bss)
    pid_x                                    0x200007c4   Data          80  mypid.o(.bss)
    pid_y                                    0x20000814   Data          80  mypid.o(.bss)
    pid_speed_left                           0x20000864   Data          80  mypid.o(.bss)
    pid_speed_right                          0x200008b4   Data          80  mypid.o(.bss)
    pid_location_left                        0x20000904   Data          80  mypid.o(.bss)
    pid_location_right                       0x20000954   Data          80  mypid.o(.bss)
    __libspace_start                         0x200009a4   Data          96  libspace.o(.bss)
    __temporary_stack_top$libspace           0x20000a04   Data           0  libspace.o(.bss)



==============================================================================

Memory Map of the image

  Image Entry point : 0x08000189

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x00006c80, Max: 0x00080000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x00006bf0, Max: 0x00080000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x00000188   Data   RO            3    RESET               startup_stm32f407xx.o
    0x08000188   0x08000188   0x00000008   Code   RO         4886  * !!!main             c_w.l(__main.o)
    0x08000190   0x08000190   0x00000034   Code   RO         5446    !!!scatter          c_w.l(__scatter.o)
    0x080001c4   0x080001c4   0x0000001a   Code   RO         5448    !!handler_copy      c_w.l(__scatter_copy.o)
    0x080001de   0x080001de   0x00000002   PAD
    0x080001e0   0x080001e0   0x0000001c   Code   RO         5450    !!handler_zi        c_w.l(__scatter_zi.o)
    0x080001fc   0x080001fc   0x00000000   Code   RO         4871    .ARM.Collect$$_printf_percent$$00000000  c_w.l(_printf_percent.o)
    0x080001fc   0x080001fc   0x00000006   Code   RO         4964    .ARM.Collect$$_printf_percent$$00000001  c_w.l(_printf_n.o)
    0x08000202   0x08000202   0x00000006   Code   RO         4966    .ARM.Collect$$_printf_percent$$00000002  c_w.l(_printf_p.o)
    0x08000208   0x08000208   0x00000006   Code   RO         4971    .ARM.Collect$$_printf_percent$$00000003  c_w.l(_printf_f.o)
    0x0800020e   0x0800020e   0x00000006   Code   RO         4972    .ARM.Collect$$_printf_percent$$00000004  c_w.l(_printf_e.o)
    0x08000214   0x08000214   0x00000006   Code   RO         4973    .ARM.Collect$$_printf_percent$$00000005  c_w.l(_printf_g.o)
    0x0800021a   0x0800021a   0x00000006   Code   RO         4974    .ARM.Collect$$_printf_percent$$00000006  c_w.l(_printf_a.o)
    0x08000220   0x08000220   0x0000000a   Code   RO         4979    .ARM.Collect$$_printf_percent$$00000007  c_w.l(_printf_ll.o)
    0x0800022a   0x0800022a   0x00000006   Code   RO         4968    .ARM.Collect$$_printf_percent$$00000008  c_w.l(_printf_i.o)
    0x08000230   0x08000230   0x00000006   Code   RO         4969    .ARM.Collect$$_printf_percent$$00000009  c_w.l(_printf_d.o)
    0x08000236   0x08000236   0x00000006   Code   RO         4970    .ARM.Collect$$_printf_percent$$0000000A  c_w.l(_printf_u.o)
    0x0800023c   0x0800023c   0x00000006   Code   RO         4967    .ARM.Collect$$_printf_percent$$0000000B  c_w.l(_printf_o.o)
    0x08000242   0x08000242   0x00000006   Code   RO         4965    .ARM.Collect$$_printf_percent$$0000000C  c_w.l(_printf_x.o)
    0x08000248   0x08000248   0x00000006   Code   RO         4976    .ARM.Collect$$_printf_percent$$0000000D  c_w.l(_printf_lli.o)
    0x0800024e   0x0800024e   0x00000006   Code   RO         4977    .ARM.Collect$$_printf_percent$$0000000E  c_w.l(_printf_lld.o)
    0x08000254   0x08000254   0x00000006   Code   RO         4978    .ARM.Collect$$_printf_percent$$0000000F  c_w.l(_printf_llu.o)
    0x0800025a   0x0800025a   0x00000006   Code   RO         4983    .ARM.Collect$$_printf_percent$$00000010  c_w.l(_printf_llo.o)
    0x08000260   0x08000260   0x00000006   Code   RO         4984    .ARM.Collect$$_printf_percent$$00000011  c_w.l(_printf_llx.o)
    0x08000266   0x08000266   0x0000000a   Code   RO         4980    .ARM.Collect$$_printf_percent$$00000012  c_w.l(_printf_l.o)
    0x08000270   0x08000270   0x00000006   Code   RO         4870    .ARM.Collect$$_printf_percent$$00000013  c_w.l(_printf_c.o)
    0x08000276   0x08000276   0x00000006   Code   RO         4963    .ARM.Collect$$_printf_percent$$00000014  c_w.l(_printf_s.o)
    0x0800027c   0x0800027c   0x00000006   Code   RO         4981    .ARM.Collect$$_printf_percent$$00000015  c_w.l(_printf_lc.o)
    0x08000282   0x08000282   0x00000006   Code   RO         4982    .ARM.Collect$$_printf_percent$$00000016  c_w.l(_printf_ls.o)
    0x08000288   0x08000288   0x00000004   Code   RO         4975    .ARM.Collect$$_printf_percent$$00000017  c_w.l(_printf_percent_end.o)
    0x0800028c   0x0800028c   0x00000002   Code   RO         5249    .ARM.Collect$$libinit$$00000000  c_w.l(libinit.o)
    0x0800028e   0x0800028e   0x00000004   Code   RO         5250    .ARM.Collect$$libinit$$00000001  c_w.l(libinit2.o)
    0x08000292   0x08000292   0x00000000   Code   RO         5253    .ARM.Collect$$libinit$$00000004  c_w.l(libinit2.o)
    0x08000292   0x08000292   0x00000000   Code   RO         5256    .ARM.Collect$$libinit$$0000000A  c_w.l(libinit2.o)
    0x08000292   0x08000292   0x00000000   Code   RO         5258    .ARM.Collect$$libinit$$0000000C  c_w.l(libinit2.o)
    0x08000292   0x08000292   0x00000000   Code   RO         5260    .ARM.Collect$$libinit$$0000000E  c_w.l(libinit2.o)
    0x08000292   0x08000292   0x00000006   Code   RO         5261    .ARM.Collect$$libinit$$0000000F  c_w.l(libinit2.o)
    0x08000298   0x08000298   0x00000000   Code   RO         5263    .ARM.Collect$$libinit$$00000011  c_w.l(libinit2.o)
    0x08000298   0x08000298   0x0000000c   Code   RO         5264    .ARM.Collect$$libinit$$00000012  c_w.l(libinit2.o)
    0x080002a4   0x080002a4   0x00000000   Code   RO         5265    .ARM.Collect$$libinit$$00000013  c_w.l(libinit2.o)
    0x080002a4   0x080002a4   0x00000000   Code   RO         5267    .ARM.Collect$$libinit$$00000015  c_w.l(libinit2.o)
    0x080002a4   0x080002a4   0x0000000a   Code   RO         5268    .ARM.Collect$$libinit$$00000016  c_w.l(libinit2.o)
    0x080002ae   0x080002ae   0x00000000   Code   RO         5269    .ARM.Collect$$libinit$$00000017  c_w.l(libinit2.o)
    0x080002ae   0x080002ae   0x00000000   Code   RO         5271    .ARM.Collect$$libinit$$00000019  c_w.l(libinit2.o)
    0x080002ae   0x080002ae   0x00000000   Code   RO         5273    .ARM.Collect$$libinit$$0000001B  c_w.l(libinit2.o)
    0x080002ae   0x080002ae   0x00000000   Code   RO         5275    .ARM.Collect$$libinit$$0000001D  c_w.l(libinit2.o)
    0x080002ae   0x080002ae   0x00000000   Code   RO         5277    .ARM.Collect$$libinit$$0000001F  c_w.l(libinit2.o)
    0x080002ae   0x080002ae   0x00000000   Code   RO         5279    .ARM.Collect$$libinit$$00000021  c_w.l(libinit2.o)
    0x080002ae   0x080002ae   0x00000000   Code   RO         5281    .ARM.Collect$$libinit$$00000023  c_w.l(libinit2.o)
    0x080002ae   0x080002ae   0x00000000   Code   RO         5283    .ARM.Collect$$libinit$$00000025  c_w.l(libinit2.o)
    0x080002ae   0x080002ae   0x00000000   Code   RO         5287    .ARM.Collect$$libinit$$0000002C  c_w.l(libinit2.o)
    0x080002ae   0x080002ae   0x00000000   Code   RO         5289    .ARM.Collect$$libinit$$0000002E  c_w.l(libinit2.o)
    0x080002ae   0x080002ae   0x00000000   Code   RO         5291    .ARM.Collect$$libinit$$00000030  c_w.l(libinit2.o)
    0x080002ae   0x080002ae   0x00000000   Code   RO         5293    .ARM.Collect$$libinit$$00000032  c_w.l(libinit2.o)
    0x080002ae   0x080002ae   0x00000002   Code   RO         5294    .ARM.Collect$$libinit$$00000033  c_w.l(libinit2.o)
    0x080002b0   0x080002b0   0x00000002   Code   RO         5439    .ARM.Collect$$libshutdown$$00000000  c_w.l(libshutdown.o)
    0x080002b2   0x080002b2   0x00000000   Code   RO         5296    .ARM.Collect$$libshutdown$$00000002  c_w.l(libshutdown2.o)
    0x080002b2   0x080002b2   0x00000000   Code   RO         5298    .ARM.Collect$$libshutdown$$00000004  c_w.l(libshutdown2.o)
    0x080002b2   0x080002b2   0x00000000   Code   RO         5300    .ARM.Collect$$libshutdown$$00000006  c_w.l(libshutdown2.o)
    0x080002b2   0x080002b2   0x00000000   Code   RO         5303    .ARM.Collect$$libshutdown$$00000009  c_w.l(libshutdown2.o)
    0x080002b2   0x080002b2   0x00000000   Code   RO         5306    .ARM.Collect$$libshutdown$$0000000C  c_w.l(libshutdown2.o)
    0x080002b2   0x080002b2   0x00000000   Code   RO         5308    .ARM.Collect$$libshutdown$$0000000E  c_w.l(libshutdown2.o)
    0x080002b2   0x080002b2   0x00000000   Code   RO         5311    .ARM.Collect$$libshutdown$$00000011  c_w.l(libshutdown2.o)
    0x080002b2   0x080002b2   0x00000002   Code   RO         5312    .ARM.Collect$$libshutdown$$00000012  c_w.l(libshutdown2.o)
    0x080002b4   0x080002b4   0x00000000   Code   RO         4906    .ARM.Collect$$rtentry$$00000000  c_w.l(__rtentry.o)
    0x080002b4   0x080002b4   0x00000000   Code   RO         5046    .ARM.Collect$$rtentry$$00000002  c_w.l(__rtentry2.o)
    0x080002b4   0x080002b4   0x00000006   Code   RO         5058    .ARM.Collect$$rtentry$$00000004  c_w.l(__rtentry4.o)
    0x080002ba   0x080002ba   0x00000000   Code   RO         5048    .ARM.Collect$$rtentry$$00000009  c_w.l(__rtentry2.o)
    0x080002ba   0x080002ba   0x00000004   Code   RO         5049    .ARM.Collect$$rtentry$$0000000A  c_w.l(__rtentry2.o)
    0x080002be   0x080002be   0x00000000   Code   RO         5051    .ARM.Collect$$rtentry$$0000000C  c_w.l(__rtentry2.o)
    0x080002be   0x080002be   0x00000008   Code   RO         5052    .ARM.Collect$$rtentry$$0000000D  c_w.l(__rtentry2.o)
    0x080002c6   0x080002c6   0x00000002   Code   RO         5321    .ARM.Collect$$rtexit$$00000000  c_w.l(rtexit.o)
    0x080002c8   0x080002c8   0x00000000   Code   RO         5380    .ARM.Collect$$rtexit$$00000002  c_w.l(rtexit2.o)
    0x080002c8   0x080002c8   0x00000004   Code   RO         5381    .ARM.Collect$$rtexit$$00000003  c_w.l(rtexit2.o)
    0x080002cc   0x080002cc   0x00000006   Code   RO         5382    .ARM.Collect$$rtexit$$00000004  c_w.l(rtexit2.o)
    0x080002d2   0x080002d2   0x00000002   PAD
    0x080002d4   0x080002d4   0x00000040   Code   RO            4    .text               startup_stm32f407xx.o
    0x08000314   0x08000314   0x000000ee   Code   RO         4838    .text               c_w.l(lludivv7m.o)
    0x08000402   0x08000402   0x00000002   PAD
    0x08000404   0x08000404   0x00000034   Code   RO         4840    .text               c_w.l(vsnprintf.o)
    0x08000438   0x08000438   0x00000052   Code   RO         4848    .text               c_w.l(_printf_str.o)
    0x0800048a   0x0800048a   0x00000002   PAD
    0x0800048c   0x0800048c   0x00000188   Code   RO         4867    .text               c_w.l(__printf_flags_ss_wp.o)
    0x08000614   0x08000614   0x0000003c   Code   RO         4872    .text               c_w.l(__0sscanf.o)
    0x08000650   0x08000650   0x0000014c   Code   RO         4874    .text               c_w.l(_scanf_int.o)
    0x0800079c   0x0800079c   0x00000016   Code   RO         4876    .text               c_w.l(abort.o)
    0x080007b2   0x080007b2   0x00000096   Code   RO         4878    .text               c_w.l(strncmp.o)
    0x08000848   0x08000848   0x0000008a   Code   RO         4880    .text               c_w.l(rt_memcpy_v6.o)
    0x080008d2   0x080008d2   0x0000004e   Code   RO         4882    .text               c_w.l(rt_memclr_w.o)
    0x08000920   0x08000920   0x00000006   Code   RO         4884    .text               c_w.l(heapauxi.o)
    0x08000926   0x08000926   0x00000002   PAD
    0x08000928   0x08000928   0x0000000c   Code   RO         4904    .text               c_w.l(sys_exit.o)
    0x08000934   0x08000934   0x0000004e   Code   RO         4913    .text               c_w.l(_printf_pad.o)
    0x08000982   0x08000982   0x00000024   Code   RO         4915    .text               c_w.l(_printf_truncate.o)
    0x080009a6   0x080009a6   0x00000002   PAD
    0x080009a8   0x080009a8   0x00000078   Code   RO         4917    .text               c_w.l(_printf_dec.o)
    0x08000a20   0x08000a20   0x00000028   Code   RO         4919    .text               c_w.l(_printf_charcount.o)
    0x08000a48   0x08000a48   0x00000030   Code   RO         4921    .text               c_w.l(_printf_char_common.o)
    0x08000a78   0x08000a78   0x0000000a   Code   RO         4923    .text               c_w.l(_sputc.o)
    0x08000a82   0x08000a82   0x00000010   Code   RO         4925    .text               c_w.l(_snputc.o)
    0x08000a92   0x08000a92   0x0000002c   Code   RO         4927    .text               c_w.l(_printf_char.o)
    0x08000abe   0x08000abe   0x00000002   PAD
    0x08000ac0   0x08000ac0   0x000000bc   Code   RO         4931    .text               c_w.l(_printf_wctomb.o)
    0x08000b7c   0x08000b7c   0x0000007c   Code   RO         4934    .text               c_w.l(_printf_longlong_dec.o)
    0x08000bf8   0x08000bf8   0x00000070   Code   RO         4940    .text               c_w.l(_printf_oct_int_ll.o)
    0x08000c68   0x08000c68   0x00000094   Code   RO         4960    .text               c_w.l(_printf_hex_int_ll_ptr.o)
    0x08000cfc   0x08000cfc   0x0000001c   Code   RO         4985    .text               c_w.l(_chval.o)
    0x08000d18   0x08000d18   0x0000002c   Code   RO         4987    .text               c_w.l(scanf_char.o)
    0x08000d44   0x08000d44   0x00000040   Code   RO         4989    .text               c_w.l(_sgetc.o)
    0x08000d84   0x08000d84   0x0000000e   Code   RO         4997    .text               c_w.l(defsig_abrt_outer.o)
    0x08000d92   0x08000d92   0x00000064   Code   RO         5001    .text               c_w.l(rt_memcpy_w.o)
    0x08000df6   0x08000df6   0x00000002   Code   RO         5042    .text               c_w.l(use_no_semi.o)
    0x08000df8   0x08000df8   0x00000000   Code   RO         5044    .text               c_w.l(indicate_semi.o)
    0x08000df8   0x08000df8   0x0000008a   Code   RO         5069    .text               c_w.l(lludiv10.o)
    0x08000e82   0x08000e82   0x00000012   Code   RO         5071    .text               c_w.l(isspace.o)
    0x08000e94   0x08000e94   0x000000b2   Code   RO         5073    .text               c_w.l(_printf_intcommon.o)
    0x08000f46   0x08000f46   0x0000041e   Code   RO         5075    .text               c_w.l(_printf_fp_dec.o)
    0x08001364   0x08001364   0x000002fc   Code   RO         5077    .text               c_w.l(_printf_fp_hex.o)
    0x08001660   0x08001660   0x0000002c   Code   RO         5082    .text               c_w.l(_printf_wchar.o)
    0x0800168c   0x0800168c   0x00000374   Code   RO         5084    .text               c_w.l(_scanf.o)
    0x08001a00   0x08001a00   0x00000040   Code   RO         5100    .text               c_w.l(_wcrtomb.o)
    0x08001a40   0x08001a40   0x0000000a   Code   RO         5102    .text               c_w.l(defsig_exit.o)
    0x08001a4a   0x08001a4a   0x00000002   PAD
    0x08001a4c   0x08001a4c   0x00000030   Code   RO         5104    .text               c_w.l(defsig_abrt_inner.o)
    0x08001a7c   0x08001a7c   0x00000008   Code   RO         5117    .text               c_w.l(libspace.o)
    0x08001a84   0x08001a84   0x0000004a   Code   RO         5122    .text               c_w.l(sys_stackheap_outer.o)
    0x08001ace   0x08001ace   0x00000002   PAD
    0x08001ad0   0x08001ad0   0x00000010   Code   RO         5124    .text               c_w.l(rt_ctype_table.o)
    0x08001ae0   0x08001ae0   0x00000008   Code   RO         5129    .text               c_w.l(rt_locale_intlibspace.o)
    0x08001ae8   0x08001ae8   0x00000080   Code   RO         5187    .text               c_w.l(_printf_fp_infnan.o)
    0x08001b68   0x08001b68   0x000000e4   Code   RO         5189    .text               c_w.l(bigflt0.o)
    0x08001c4c   0x08001c4c   0x00000012   Code   RO         5230    .text               c_w.l(exit.o)
    0x08001c5e   0x08001c5e   0x00000032   Code   RO         5234    .text               c_w.l(defsig_general.o)
    0x08001c90   0x08001c90   0x0000000e   Code   RO         5319    .text               c_w.l(sys_wrch.o)
    0x08001c9e   0x08001c9e   0x00000002   PAD
    0x08001ca0   0x08001ca0   0x00000080   Code   RO         5373    .text               c_w.l(strcmpv7m.o)
    0x08001d20   0x08001d20   0x0000003e   Code   RO         5192    CL$$btod_d2e        c_w.l(btod.o)
    0x08001d5e   0x08001d5e   0x00000046   Code   RO         5194    CL$$btod_d2e_denorm_low  c_w.l(btod.o)
    0x08001da4   0x08001da4   0x00000060   Code   RO         5193    CL$$btod_d2e_norm_op1  c_w.l(btod.o)
    0x08001e04   0x08001e04   0x00000338   Code   RO         5202    CL$$btod_div_common  c_w.l(btod.o)
    0x0800213c   0x0800213c   0x000000dc   Code   RO         5199    CL$$btod_e2e        c_w.l(btod.o)
    0x08002218   0x08002218   0x0000002a   Code   RO         5196    CL$$btod_ediv       c_w.l(btod.o)
    0x08002242   0x08002242   0x0000002a   Code   RO         5195    CL$$btod_emul       c_w.l(btod.o)
    0x0800226c   0x0800226c   0x00000244   Code   RO         5201    CL$$btod_mult_common  c_w.l(btod.o)
    0x080024b0   0x080024b0   0x00000002   Code   RO          517    i.BusFault_Handler  stm32f4xx_it.o
    0x080024b2   0x080024b2   0x00000002   PAD
    0x080024b4   0x080024b4   0x0000000c   Code   RO          518    i.DMA1_Stream2_IRQHandler  stm32f4xx_it.o
    0x080024c0   0x080024c0   0x0000000c   Code   RO          519    i.DMA1_Stream5_IRQHandler  stm32f4xx_it.o
    0x080024cc   0x080024cc   0x0000000c   Code   RO          520    i.DMA2_Stream1_IRQHandler  stm32f4xx_it.o
    0x080024d8   0x080024d8   0x00000028   Code   RO         1579    i.DMA_CalcBaseAndBitshift  stm32f4xx_hal_dma.o
    0x08002500   0x08002500   0x00000054   Code   RO         1580    i.DMA_CheckFifoParam  stm32f4xx_hal_dma.o
    0x08002554   0x08002554   0x00000028   Code   RO         1581    i.DMA_SetConfig     stm32f4xx_hal_dma.o
    0x0800257c   0x0800257c   0x00000002   Code   RO          521    i.DebugMon_Handler  stm32f4xx_it.o
    0x0800257e   0x0800257e   0x00000038   Code   RO         4569    i.Emm_V5_En_Control  emm_v5.o
    0x080025b6   0x080025b6   0x000001ec   Code   RO         4575    i.Emm_V5_Parse_Response  emm_v5.o
    0x080027a2   0x080027a2   0x0000008e   Code   RO         4577    i.Emm_V5_Read_Sys_Params  emm_v5.o
    0x08002830   0x08002830   0x00000030   Code   RO         4579    i.Emm_V5_Reset_CurPos_To_Zero  emm_v5.o
    0x08002860   0x08002860   0x00000034   Code   RO         4580    i.Emm_V5_Stop_Now   emm_v5.o
    0x08002894   0x08002894   0x000000c8   Code   RO         4582    i.Emm_V5_Vel_Control  emm_v5.o
    0x0800295c   0x0800295c   0x00000004   Code   RO           13    i.Error_Handler     main.o
    0x08002960   0x08002960   0x00000092   Code   RO         1582    i.HAL_DMA_Abort     stm32f4xx_hal_dma.o
    0x080029f2   0x080029f2   0x00000024   Code   RO         1583    i.HAL_DMA_Abort_IT  stm32f4xx_hal_dma.o
    0x08002a16   0x08002a16   0x00000002   PAD
    0x08002a18   0x08002a18   0x000001a0   Code   RO         1587    i.HAL_DMA_IRQHandler  stm32f4xx_hal_dma.o
    0x08002bb8   0x08002bb8   0x000000d4   Code   RO         1588    i.HAL_DMA_Init      stm32f4xx_hal_dma.o
    0x08002c8c   0x08002c8c   0x0000006e   Code   RO         1592    i.HAL_DMA_Start_IT  stm32f4xx_hal_dma.o
    0x08002cfa   0x08002cfa   0x00000002   PAD
    0x08002cfc   0x08002cfc   0x00000024   Code   RO         2019    i.HAL_Delay         stm32f4xx_hal.o
    0x08002d20   0x08002d20   0x000001f0   Code   RO         1475    i.HAL_GPIO_Init     stm32f4xx_hal_gpio.o
    0x08002f10   0x08002f10   0x0000000a   Code   RO         1479    i.HAL_GPIO_WritePin  stm32f4xx_hal_gpio.o
    0x08002f1a   0x08002f1a   0x00000002   PAD
    0x08002f1c   0x08002f1c   0x0000000c   Code   RO         2025    i.HAL_GetTick       stm32f4xx_hal.o
    0x08002f28   0x08002f28   0x00000188   Code   RO          676    i.HAL_I2C_Init      stm32f4xx_hal_i2c.o
    0x080030b0   0x080030b0   0x00000130   Code   RO          697    i.HAL_I2C_Mem_Write  stm32f4xx_hal_i2c.o
    0x080031e0   0x080031e0   0x000000ac   Code   RO          314    i.HAL_I2C_MspInit   i2c.o
    0x0800328c   0x0800328c   0x00000010   Code   RO         2031    i.HAL_IncTick       stm32f4xx_hal.o
    0x0800329c   0x0800329c   0x00000034   Code   RO         2032    i.HAL_Init          stm32f4xx_hal.o
    0x080032d0   0x080032d0   0x00000040   Code   RO         2033    i.HAL_InitTick      stm32f4xx_hal.o
    0x08003310   0x08003310   0x00000030   Code   RO          641    i.HAL_MspInit       stm32f4xx_hal_msp.o
    0x08003340   0x08003340   0x0000001a   Code   RO         1867    i.HAL_NVIC_EnableIRQ  stm32f4xx_hal_cortex.o
    0x0800335a   0x0800335a   0x00000002   PAD
    0x0800335c   0x0800335c   0x00000040   Code   RO         1873    i.HAL_NVIC_SetPriority  stm32f4xx_hal_cortex.o
    0x0800339c   0x0800339c   0x00000024   Code   RO         1874    i.HAL_NVIC_SetPriorityGrouping  stm32f4xx_hal_cortex.o
    0x080033c0   0x080033c0   0x00000134   Code   RO         1121    i.HAL_RCC_ClockConfig  stm32f4xx_hal_rcc.o
    0x080034f4   0x080034f4   0x00000020   Code   RO         1128    i.HAL_RCC_GetPCLK1Freq  stm32f4xx_hal_rcc.o
    0x08003514   0x08003514   0x00000020   Code   RO         1129    i.HAL_RCC_GetPCLK2Freq  stm32f4xx_hal_rcc.o
    0x08003534   0x08003534   0x00000064   Code   RO         1130    i.HAL_RCC_GetSysClockFreq  stm32f4xx_hal_rcc.o
    0x08003598   0x08003598   0x0000036c   Code   RO         1133    i.HAL_RCC_OscConfig  stm32f4xx_hal_rcc.o
    0x08003904   0x08003904   0x00000028   Code   RO         1878    i.HAL_SYSTICK_Config  stm32f4xx_hal_cortex.o
    0x0800392c   0x0800392c   0x00000054   Code   RO         2979    i.HAL_TIMEx_ConfigBreakDeadTime  stm32f4xx_hal_tim_ex.o
    0x08003980   0x08003980   0x00000090   Code   RO         2995    i.HAL_TIMEx_MasterConfigSynchronization  stm32f4xx_hal_tim_ex.o
    0x08003a10   0x08003a10   0x0000005a   Code   RO         2272    i.HAL_TIM_Base_Init  stm32f4xx_hal_tim.o
    0x08003a6a   0x08003a6a   0x00000002   PAD
    0x08003a6c   0x08003a6c   0x00000028   Code   RO          362    i.HAL_TIM_Base_MspInit  tim.o
    0x08003a94   0x08003a94   0x000000dc   Code   RO         2281    i.HAL_TIM_ConfigClockSource  stm32f4xx_hal_tim.o
    0x08003b70   0x08003b70   0x000000a4   Code   RO         2293    i.HAL_TIM_Encoder_Init  stm32f4xx_hal_tim.o
    0x08003c14   0x08003c14   0x000000a4   Code   RO          364    i.HAL_TIM_Encoder_MspInit  tim.o
    0x08003cb8   0x08003cb8   0x00000054   Code   RO          365    i.HAL_TIM_MspPostInit  tim.o
    0x08003d0c   0x08003d0c   0x000000cc   Code   RO         2344    i.HAL_TIM_PWM_ConfigChannel  stm32f4xx_hal_tim.o
    0x08003dd8   0x08003dd8   0x0000005a   Code   RO         2347    i.HAL_TIM_PWM_Init  stm32f4xx_hal_tim.o
    0x08003e32   0x08003e32   0x00000002   Code   RO         2349    i.HAL_TIM_PWM_MspInit  stm32f4xx_hal_tim.o
    0x08003e34   0x08003e34   0x0000004a   Code   RO         3253    i.HAL_UARTEx_ReceiveToIdle_DMA  stm32f4xx_hal_uart.o
    0x08003e7e   0x08003e7e   0x00000002   PAD
    0x08003e80   0x08003e80   0x00000080   Code   RO          433    i.HAL_UARTEx_RxEventCallback  usart.o
    0x08003f00   0x08003f00   0x00000002   Code   RO         3269    i.HAL_UART_ErrorCallback  stm32f4xx_hal_uart.o
    0x08003f02   0x08003f02   0x00000002   PAD
    0x08003f04   0x08003f04   0x00000280   Code   RO         3272    i.HAL_UART_IRQHandler  stm32f4xx_hal_uart.o
    0x08004184   0x08004184   0x00000064   Code   RO         3273    i.HAL_UART_Init     stm32f4xx_hal_uart.o
    0x080041e8   0x080041e8   0x000002d8   Code   RO          435    i.HAL_UART_MspInit  usart.o
    0x080044c0   0x080044c0   0x00000002   Code   RO         3279    i.HAL_UART_RxCpltCallback  stm32f4xx_hal_uart.o
    0x080044c2   0x080044c2   0x00000002   Code   RO         3280    i.HAL_UART_RxHalfCpltCallback  stm32f4xx_hal_uart.o
    0x080044c4   0x080044c4   0x000000a0   Code   RO         3281    i.HAL_UART_Transmit  stm32f4xx_hal_uart.o
    0x08004564   0x08004564   0x00000002   Code   RO         3284    i.HAL_UART_TxCpltCallback  stm32f4xx_hal_uart.o
    0x08004566   0x08004566   0x00000002   Code   RO          522    i.HardFault_Handler  stm32f4xx_it.o
    0x08004568   0x08004568   0x0000002e   Code   RO          719    i.I2C_IsAcknowledgeFailed  stm32f4xx_hal_i2c.o
    0x08004596   0x08004596   0x00000002   PAD
    0x08004598   0x08004598   0x000000a8   Code   RO          730    i.I2C_RequestMemoryWrite  stm32f4xx_hal_i2c.o
    0x08004640   0x08004640   0x00000056   Code   RO          734    i.I2C_WaitOnBTFFlagUntilTimeout  stm32f4xx_hal_i2c.o
    0x08004696   0x08004696   0x00000002   PAD
    0x08004698   0x08004698   0x00000090   Code   RO          735    i.I2C_WaitOnFlagUntilTimeout  stm32f4xx_hal_i2c.o
    0x08004728   0x08004728   0x000000bc   Code   RO          736    i.I2C_WaitOnMasterAddressFlagUntilTimeout  stm32f4xx_hal_i2c.o
    0x080047e4   0x080047e4   0x00000056   Code   RO          738    i.I2C_WaitOnTXEFlagUntilTimeout  stm32f4xx_hal_i2c.o
    0x0800483a   0x0800483a   0x00000002   PAD
    0x0800483c   0x0800483c   0x0000005c   Code   RO          289    i.MX_DMA_Init       dma.o
    0x08004898   0x08004898   0x00000120   Code   RO          265    i.MX_GPIO_Init      gpio.o
    0x080049b8   0x080049b8   0x00000040   Code   RO          315    i.MX_I2C1_Init      i2c.o
    0x080049f8   0x080049f8   0x00000040   Code   RO          316    i.MX_I2C2_Init      i2c.o
    0x08004a38   0x08004a38   0x000000d8   Code   RO          366    i.MX_TIM1_Init      tim.o
    0x08004b10   0x08004b10   0x0000006c   Code   RO          367    i.MX_TIM3_Init      tim.o
    0x08004b7c   0x08004b7c   0x0000006c   Code   RO          368    i.MX_TIM4_Init      tim.o
    0x08004be8   0x08004be8   0x00000050   Code   RO          436    i.MX_UART4_Init     usart.o
    0x08004c38   0x08004c38   0x00000038   Code   RO          437    i.MX_UART5_Init     usart.o
    0x08004c70   0x08004c70   0x00000038   Code   RO          438    i.MX_USART1_UART_Init  usart.o
    0x08004ca8   0x08004ca8   0x00000050   Code   RO          439    i.MX_USART2_UART_Init  usart.o
    0x08004cf8   0x08004cf8   0x00000038   Code   RO          440    i.MX_USART3_UART_Init  usart.o
    0x08004d30   0x08004d30   0x00000050   Code   RO          441    i.MX_USART6_UART_Init  usart.o
    0x08004d80   0x08004d80   0x00000002   Code   RO          523    i.MemManage_Handler  stm32f4xx_it.o
    0x08004d82   0x08004d82   0x00000002   Code   RO          524    i.NMI_Handler       stm32f4xx_it.o
    0x08004d84   0x08004d84   0x00000034   Code   RO         3645    i.OLED_Clear        oled.o
    0x08004db8   0x08004db8   0x00000030   Code   RO         3648    i.OLED_Init         oled.o
    0x08004de8   0x08004de8   0x00000022   Code   RO         3649    i.OLED_Set_Position  oled.o
    0x08004e0a   0x08004e0a   0x00000002   PAD
    0x08004e0c   0x08004e0c   0x00000024   Code   RO         3657    i.OLED_Write_cmd    oled.o
    0x08004e30   0x08004e30   0x00000024   Code   RO         3658    i.OLED_Write_data   oled.o
    0x08004e54   0x08004e54   0x000000d0   Code   RO         4674    i.PID_INIT          mypid.o
    0x08004f24   0x08004f24   0x00000044   Code   RO         4675    i.PID_struct_init   mypid.o
    0x08004f68   0x08004f68   0x00000002   Code   RO          525    i.PendSV_Handler    stm32f4xx_it.o
    0x08004f6a   0x08004f6a   0x00000002   Code   RO          526    i.SVC_Handler       stm32f4xx_it.o
    0x08004f6c   0x08004f6c   0x0000002c   Code   RO         4205    i.Step_Motor_Init   step_motor_bsp.o
    0x08004f98   0x08004f98   0x000000c4   Code   RO         4208    i.Step_Motor_Set_Speed_my  step_motor_bsp.o
    0x0800505c   0x0800505c   0x00000024   Code   RO         4209    i.Step_Motor_Stop   step_motor_bsp.o
    0x08005080   0x08005080   0x00000004   Code   RO          527    i.SysTick_Handler   stm32f4xx_it.o
    0x08005084   0x08005084   0x00000090   Code   RO           14    i.SystemClock_Config  main.o
    0x08005114   0x08005114   0x00000010   Code   RO         3607    i.SystemInit        system_stm32f4xx.o
    0x08005124   0x08005124   0x000000d0   Code   RO         2365    i.TIM_Base_SetConfig  stm32f4xx_hal_tim.o
    0x080051f4   0x080051f4   0x00000014   Code   RO         2376    i.TIM_ETR_SetConfig  stm32f4xx_hal_tim.o
    0x08005208   0x08005208   0x00000010   Code   RO         2377    i.TIM_ITRx_SetConfig  stm32f4xx_hal_tim.o
    0x08005218   0x08005218   0x00000060   Code   RO         2378    i.TIM_OC1_SetConfig  stm32f4xx_hal_tim.o
    0x08005278   0x08005278   0x0000006c   Code   RO         2379    i.TIM_OC2_SetConfig  stm32f4xx_hal_tim.o
    0x080052e4   0x080052e4   0x00000068   Code   RO         2380    i.TIM_OC3_SetConfig  stm32f4xx_hal_tim.o
    0x0800534c   0x0800534c   0x00000050   Code   RO         2381    i.TIM_OC4_SetConfig  stm32f4xx_hal_tim.o
    0x0800539c   0x0800539c   0x00000022   Code   RO         2383    i.TIM_TI1_ConfigInputStage  stm32f4xx_hal_tim.o
    0x080053be   0x080053be   0x00000024   Code   RO         2385    i.TIM_TI2_ConfigInputStage  stm32f4xx_hal_tim.o
    0x080053e2   0x080053e2   0x00000002   PAD
    0x080053e4   0x080053e4   0x0000002c   Code   RO          528    i.UART4_IRQHandler  stm32f4xx_it.o
    0x08005410   0x08005410   0x0000000e   Code   RO         3286    i.UART_DMAAbortOnError  stm32f4xx_hal_uart.o
    0x0800541e   0x0800541e   0x0000004a   Code   RO         3287    i.UART_DMAError     stm32f4xx_hal_uart.o
    0x08005468   0x08005468   0x00000086   Code   RO         3288    i.UART_DMAReceiveCplt  stm32f4xx_hal_uart.o
    0x080054ee   0x080054ee   0x0000001e   Code   RO         3290    i.UART_DMARxHalfCplt  stm32f4xx_hal_uart.o
    0x0800550c   0x0800550c   0x0000004e   Code   RO         3296    i.UART_EndRxTransfer  stm32f4xx_hal_uart.o
    0x0800555a   0x0800555a   0x0000001c   Code   RO         3297    i.UART_EndTxTransfer  stm32f4xx_hal_uart.o
    0x08005576   0x08005576   0x000000c2   Code   RO         3298    i.UART_Receive_IT   stm32f4xx_hal_uart.o
    0x08005638   0x08005638   0x0000010c   Code   RO         3299    i.UART_SetConfig    stm32f4xx_hal_uart.o
    0x08005744   0x08005744   0x000000a0   Code   RO         3300    i.UART_Start_Receive_DMA  stm32f4xx_hal_uart.o
    0x080057e4   0x080057e4   0x00000072   Code   RO         3302    i.UART_WaitOnFlagUntilTimeout  stm32f4xx_hal_uart.o
    0x08005856   0x08005856   0x00000002   PAD
    0x08005858   0x08005858   0x0000000c   Code   RO          529    i.USART1_IRQHandler  stm32f4xx_it.o
    0x08005864   0x08005864   0x0000002c   Code   RO          530    i.USART2_IRQHandler  stm32f4xx_it.o
    0x08005890   0x08005890   0x0000000c   Code   RO          531    i.USART3_IRQHandler  stm32f4xx_it.o
    0x0800589c   0x0800589c   0x0000002c   Code   RO          532    i.USART6_IRQHandler  stm32f4xx_it.o
    0x080058c8   0x080058c8   0x00000002   Code   RO          533    i.UsageFault_Handler  stm32f4xx_it.o
    0x080058ca   0x080058ca   0x00000030   Code   RO         5317    i.__ARM_fpclassify  m_wm.l(fpclassify.o)
    0x080058fa   0x080058fa   0x00000020   Code   RO         1880    i.__NVIC_SetPriority  stm32f4xx_hal_cortex.o
    0x0800591a   0x0800591a   0x0000000e   Code   RO         4860    i._is_digit         c_w.l(__printf_wp.o)
    0x08005928   0x08005928   0x0000002a   Code   RO         4676    i.abs_limit         mypid.o
    0x08005952   0x08005952   0x00000002   PAD
    0x08005954   0x08005954   0x0000002c   Code   RO         4116    i.calc_motor_angle  uart_bsp.o
    0x08005980   0x08005980   0x00000048   Code   RO         4117    i.calc_relative_angle  uart_bsp.o
    0x080059c8   0x080059c8   0x000000a8   Code   RO           15    i.main              main.o
    0x08005a70   0x08005a70   0x00000034   Code   RO         4119    i.my_printf         uart_bsp.o
    0x08005aa4   0x08005aa4   0x00000314   Code   RO         4120    i.parse_x_motor_data  uart_bsp.o
    0x08005db8   0x08005db8   0x00000314   Code   RO         4121    i.parse_y_motor_data  uart_bsp.o
    0x080060cc   0x080060cc   0x000000e4   Code   RO         4259    i.pi_parse_data     pi_bsp.o
    0x080061b0   0x080061b0   0x00000060   Code   RO         4260    i.pi_proc           pi_bsp.o
    0x08006210   0x08006210   0x00000194   Code   RO         4678    i.pid_calc          mypid.o
    0x080063a4   0x080063a4   0x0000000e   Code   RO         4682    i.pid_param_init    mypid.o
    0x080063b2   0x080063b2   0x00000002   PAD
    0x080063b4   0x080063b4   0x00000020   Code   RO         4683    i.pid_reset         mypid.o
    0x080063d4   0x080063d4   0x00000030   Code   RO         3757    i.rt_ringbuffer_data_len  ringbuffer.o
    0x08006404   0x08006404   0x00000074   Code   RO         3758    i.rt_ringbuffer_get  ringbuffer.o
    0x08006478   0x08006478   0x00000030   Code   RO         3760    i.rt_ringbuffer_init  ringbuffer.o
    0x080064a8   0x080064a8   0x00000078   Code   RO         3762    i.rt_ringbuffer_put  ringbuffer.o
    0x08006520   0x08006520   0x00000020   Code   RO         3767    i.rt_ringbuffer_status  ringbuffer.o
    0x08006540   0x08006540   0x00000054   Code   RO         4124    i.save_initial_position  uart_bsp.o
    0x08006594   0x08006594   0x0000000c   Code   RO         3842    i.schedule_init     schedule.o
    0x080065a0   0x080065a0   0x0000003c   Code   RO         3843    i.schedule_run      schedule.o
    0x080065dc   0x080065dc   0x000001c8   Code   RO         4125    i.uart_proc         uart_bsp.o
    0x080067a4   0x080067a4   0x0000002c   Code   RO         5228    locale$$code        c_w.l(lc_numeric_c.o)
    0x080067d0   0x080067d0   0x0000002c   Code   RO         5361    locale$$code        c_w.l(lc_ctype_c.o)
    0x080067fc   0x080067fc   0x0000000c   Code   RO         5013    x$fpl$dretinf       fz_wm.l(dretinf.o)
    0x08006808   0x08006808   0x00000056   Code   RO         4890    x$fpl$f2d           fz_wm.l(f2d.o)
    0x0800685e   0x0800685e   0x0000008c   Code   RO         5015    x$fpl$fnaninf       fz_wm.l(fnaninf.o)
    0x080068ea   0x080068ea   0x0000000a   Code   RO         5377    x$fpl$fpinit        fz_wm.l(fpinit.o)
    0x080068f4   0x080068f4   0x00000004   Code   RO         5019    x$fpl$printf1       fz_wm.l(printf1.o)
    0x080068f8   0x080068f8   0x00000004   Code   RO         5021    x$fpl$printf2       fz_wm.l(printf2.o)
    0x080068fc   0x080068fc   0x00000000   Code   RO         5027    x$fpl$usenofp       fz_wm.l(usenofp.o)
    0x080068fc   0x080068fc   0x00000008   Data   RO         1594    .constdata          stm32f4xx_hal_dma.o
    0x08006904   0x08006904   0x00000010   Data   RO         3608    .constdata          system_stm32f4xx.o
    0x08006914   0x08006914   0x00000008   Data   RO         3609    .constdata          system_stm32f4xx.o
    0x0800691c   0x0800691c   0x00000011   Data   RO         4868    .constdata          c_w.l(__printf_flags_ss_wp.o)
    0x0800692d   0x0800692d   0x00000003   PAD
    0x08006930   0x08006930   0x00000008   Data   RO         4932    .constdata          c_w.l(_printf_wctomb.o)
    0x08006938   0x08006938   0x00000028   Data   RO         4961    .constdata          c_w.l(_printf_hex_int_ll_ptr.o)
    0x08006960   0x08006960   0x00000026   Data   RO         5078    .constdata          c_w.l(_printf_fp_hex.o)
    0x08006986   0x08006986   0x00000002   PAD
    0x08006988   0x08006988   0x00000094   Data   RO         5190    .constdata          c_w.l(bigflt0.o)
    0x08006a1c   0x08006a1c   0x00000087   Data   RO         4130    .conststring        uart_bsp.o
    0x08006aa3   0x08006aa3   0x00000001   PAD
    0x08006aa4   0x08006aa4   0x00000020   Data   RO         5444    Region$$Table       anon$$obj.o
    0x08006ac4   0x08006ac4   0x0000001c   Data   RO         5227    locale$$data        c_w.l(lc_numeric_c.o)
    0x08006ae0   0x08006ae0   0x00000110   Data   RO         5360    locale$$data        c_w.l(lc_ctype_c.o)


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x08006bf0, Size: 0x00001008, Max: 0x0001c000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x08006bf0   0x0000000c   Data   RW         2039    .data               stm32f4xx_hal.o
    0x2000000c   0x08006bfc   0x00000004   Data   RW         3610    .data               system_stm32f4xx.o
    0x20000010   0x08006c00   0x00000016   Data   RW         3660    .data               oled.o
    0x20000026   0x08006c16   0x00000002   PAD
    0x20000028   0x08006c18   0x0000001c   Data   RW         3844    .data               schedule.o
    0x20000044   0x08006c34   0x0000002c   Data   RW         4131    .data               uart_bsp.o
    0x20000070   0x08006c60   0x00000020   Data   RW         4261    .data               pi_bsp.o
    0x20000090        -       0x000000a8   Zero   RW          317    .bss                i2c.o
    0x20000138        -       0x000000d8   Zero   RW          369    .bss                tim.o
    0x20000210        -       0x00000390   Zero   RW          442    .bss                usart.o
    0x200005a0        -       0x00000164   Zero   RW         4126    .bss                uart_bsp.o
    0x20000704        -       0x00000040   Zero   RW         4127    .bss                uart_bsp.o
    0x20000744        -       0x00000040   Zero   RW         4128    .bss                uart_bsp.o
    0x20000784        -       0x00000040   Zero   RW         4129    .bss                uart_bsp.o
    0x200007c4        -       0x000001e0   Zero   RW         4685    .bss                mypid.o
    0x200009a4        -       0x00000060   Zero   RW         5118    .bss                c_w.l(libspace.o)
    0x20000a04   0x08006c80   0x00000004   PAD
    0x20000a08        -       0x00000200   Zero   RW            2    HEAP                startup_stm32f407xx.o
    0x20000c08        -       0x00000400   Zero   RW            1    STACK               startup_stm32f407xx.o


    Execution Region RW_IRAM2 (Exec base: 0x2001c000, Load base: 0x08006c80, Size: 0x00000000, Max: 0x00004000, ABSOLUTE)

    **** No section assigned to this execution region ****


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

        92          4          0          0          0        902   dma.o
       990        138          0          0          0       6470   emm_v5.o
       288         20          0          0          0       1163   gpio.o
       300         46          0          0        168       2546   i2c.o
       316         42          0          0          0     704404   main.o
       768         54          0          0        480       6374   mypid.o
       206         14          0         22          0       3994   oled.o
         0          0          0          0          0        564   oled_bsp.o
       324        118          0         32          0       2168   pi_bsp.o
       364          0          0          0          0       6311   ringbuffer.o
        72          8          0         28          0       3191   schedule.o
        64         26        392          0       1536        872   startup_stm32f407xx.o
       276         38          0          0          0       2342   step_motor_bsp.o
       180         28          0         12          0       9709   stm32f4xx_hal.o
       198         14          0          0          0      34039   stm32f4xx_hal_cortex.o
      1084         16          8          0          0       7682   stm32f4xx_hal_dma.o
       506         46          0          0          0       2300   stm32f4xx_hal_gpio.o
      1414         32          0          0          0       9963   stm32f4xx_hal_i2c.o
        48          6          0          0          0        934   stm32f4xx_hal_msp.o
      1348         76          0          0          0       5476   stm32f4xx_hal_rcc.o
      1472         80          0          0          0      13055   stm32f4xx_hal_tim.o
       228         28          0          0          0       2304   stm32f4xx_hal_tim_ex.o
      2076         28          0          0          0      15653   stm32f4xx_hal_uart.o
       212         66          0          0          0       8687   stm32f4xx_it.o
        16          4         24          4          0       1235   system_stm32f4xx.o
       720         74          0          0        216       4840   tim.o
      2284       1160        135         44        548       7718   uart_bsp.o
      1264        154          0          0        912       6834   usart.o

    ----------------------------------------------------------------------
     17142       <USER>        <GROUP>        144       3860     871730   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        32          0          1          2          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

        60          8          0          0          0         84   __0sscanf.o
         8          0          0          0          0         68   __main.o
       392          4         17          0          0         92   __printf_flags_ss_wp.o
        14          0          0          0          0         68   __printf_wp.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        52          8          0          0          0          0   __scatter.o
        26          0          0          0          0          0   __scatter_copy.o
        28          0          0          0          0          0   __scatter_zi.o
        28          0          0          0          0         68   _chval.o
         6          0          0          0          0          0   _printf_a.o
         6          0          0          0          0          0   _printf_c.o
        44          0          0          0          0        108   _printf_char.o
        48          6          0          0          0         96   _printf_char_common.o
        40          0          0          0          0         68   _printf_charcount.o
         6          0          0          0          0          0   _printf_d.o
       120         16          0          0          0         92   _printf_dec.o
         6          0          0          0          0          0   _printf_e.o
         6          0          0          0          0          0   _printf_f.o
      1054          0          0          0          0        216   _printf_fp_dec.o
       764          8         38          0          0        100   _printf_fp_hex.o
       128         16          0          0          0         84   _printf_fp_infnan.o
         6          0          0          0          0          0   _printf_g.o
       148          4         40          0          0        160   _printf_hex_int_ll_ptr.o
         6          0          0          0          0          0   _printf_i.o
       178          0          0          0          0         88   _printf_intcommon.o
        10          0          0          0          0          0   _printf_l.o
         6          0          0          0          0          0   _printf_lc.o
        10          0          0          0          0          0   _printf_ll.o
         6          0          0          0          0          0   _printf_lld.o
         6          0          0          0          0          0   _printf_lli.o
         6          0          0          0          0          0   _printf_llo.o
         6          0          0          0          0          0   _printf_llu.o
         6          0          0          0          0          0   _printf_llx.o
       124         16          0          0          0         92   _printf_longlong_dec.o
         6          0          0          0          0          0   _printf_ls.o
         6          0          0          0          0          0   _printf_n.o
         6          0          0          0          0          0   _printf_o.o
       112         10          0          0          0        124   _printf_oct_int_ll.o
         6          0          0          0          0          0   _printf_p.o
        78          0          0          0          0        108   _printf_pad.o
         0          0          0          0          0          0   _printf_percent.o
         4          0          0          0          0          0   _printf_percent_end.o
         6          0          0          0          0          0   _printf_s.o
        82          0          0          0          0         80   _printf_str.o
        36          0          0          0          0         84   _printf_truncate.o
         6          0          0          0          0          0   _printf_u.o
        44          0          0          0          0        108   _printf_wchar.o
       188          6          8          0          0         92   _printf_wctomb.o
         6          0          0          0          0          0   _printf_x.o
       884          4          0          0          0        100   _scanf.o
       332          0          0          0          0         96   _scanf_int.o
        64          0          0          0          0         84   _sgetc.o
        16          0          0          0          0         68   _snputc.o
        10          0          0          0          0         68   _sputc.o
        64          0          0          0          0         92   _wcrtomb.o
        22          0          0          0          0         80   abort.o
       228          4        148          0          0         96   bigflt0.o
      1936        128          0          0          0        672   btod.o
        48         34          0          0          0         76   defsig_abrt_inner.o
        14          0          0          0          0         80   defsig_abrt_outer.o
        10          0          0          0          0         68   defsig_exit.o
        50          0          0          0          0         88   defsig_general.o
        18          0          0          0          0         80   exit.o
         6          0          0          0          0        152   heapauxi.o
         0          0          0          0          0          0   indicate_semi.o
        18          0          0          0          0         76   isspace.o
        44         10        272          0          0         76   lc_ctype_c.o
        44         10         28          0          0         76   lc_numeric_c.o
         2          0          0          0          0          0   libinit.o
        34          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         2          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
       138          0          0          0          0         80   lludiv10.o
       238          0          0          0          0        100   lludivv7m.o
        16          4          0          0          0         76   rt_ctype_table.o
         8          4          0          0          0         68   rt_locale_intlibspace.o
        78          0          0          0          0         80   rt_memclr_w.o
       138          0          0          0          0         68   rt_memcpy_v6.o
       100          0          0          0          0         80   rt_memcpy_w.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
        44          8          0          0          0         84   scanf_char.o
       128          0          0          0          0         68   strcmpv7m.o
       150          0          0          0          0         80   strncmp.o
        12          4          0          0          0         68   sys_exit.o
        74          0          0          0          0         80   sys_stackheap_outer.o
        14          0          0          0          0         76   sys_wrch.o
         2          0          0          0          0         68   use_no_semi.o
        52          4          0          0          0         80   vsnprintf.o
        12          0          0          0          0        116   dretinf.o
        86          4          0          0          0        132   f2d.o
       140          4          0          0          0        132   fnaninf.o
        10          0          0          0          0        116   fpinit.o
         4          0          0          0          0        116   printf1.o
         4          0          0          0          0        116   printf2.o
         0          0          0          0          0          0   usenofp.o
        48          0          0          0          0        124   fpclassify.o

    ----------------------------------------------------------------------
      9342        <USER>        <GROUP>          0        100       6284   Library Totals
        20          0          5          0          4          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      9018        320        551          0         96       5432   c_w.l
       256          8          0          0          0        728   fz_wm.l
        48          0          0          0          0        124   m_wm.l

    ----------------------------------------------------------------------
      9342        <USER>        <GROUP>          0        100       6284   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     26484       2648       1148        144       3960     860946   Grand Totals
     26484       2648       1148        144       3960     860946   ELF Image Totals
     26484       2648       1148        144          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                27632 (  26.98kB)
    Total RW  Size (RW Data + ZI Data)              4104 (   4.01kB)
    Total ROM Size (Code + RO Data + RW Data)      27776 (  27.13kB)

==============================================================================

